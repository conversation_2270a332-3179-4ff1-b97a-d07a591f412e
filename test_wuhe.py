"""
测试天干五合的计算
"""

from comprehensive_result import ComprehensiveAnalyzer

def test_wuhe_calculation():
    """测试天干五合的计算"""
    analyzer = ComprehensiveAnalyzer()
    
    # 创建一个包含天干五合的测试案例
    # 假设流年是甲年，原局有己干，形成甲己合土
    
    # 模拟用户信息 - 选择一个可能有天干五合的日期
    result = analyzer.analyze_complete_bazi(
        birth_year=1984,  # 甲子年
        birth_month=5,
        birth_day=15,
        birth_hour=15,
        birth_minute=0,
        city_name="广州",
        name="五合测试",
        gender="男"
    )
    
    # 检查结果中是否有天干五合的计算
    print("=== 检查天干五合计算 ===")
    
    # 检查流年计算详情
    if '流年计算详情' in result['大运流年']:
        liunian_details = result['大运流年']['流年计算详情']
        if '第四步_流年与原局作用关系' in liunian_details:
            tiangan_zuoyong = liunian_details['第四步_流年与原局作用关系']['天干作用详情']
            
            print("天干作用详情：")
            for zuoyong in tiangan_zuoyong:
                print(f"  {zuoyong['原局']} - {zuoyong['五行关系']}")
                if '计算详情' in zuoyong:
                    print(f"    计算详情: {zuoyong['计算详情']}")
                print(f"    得分: {zuoyong['得分']}")
    
    # 保存详细结果用于检查
    detailed_result = analyzer.format_comprehensive_output(result, show_details=True)
    with open("天干五合测试_详细过程.txt", "w", encoding="utf-8") as f:
        f.write(detailed_result)
    
    print("详细结果已保存到: 天干五合测试_详细过程.txt")

if __name__ == "__main__":
    test_wuhe_calculation()
