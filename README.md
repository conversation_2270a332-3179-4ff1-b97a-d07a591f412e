# 八字占卜系统

一个完整的八字占卜分析系统，支持阳历阴历换算、五行计算、十神分析、大运流年分析等功能。

## 功能特性

- **阳历阴历换算模块**：真太阳时计算、四柱八字推算
- **五行计算模块**：五行力量分析、藏干计算、季节旺度分析
- **十神计算模块**：十神识别、身强身弱判断、喜用神忌神分析
- **流年大运计算模块**：大运起运、流年流月流日分析
- **综合结果模块**：汇总所有分析结果，提供完整报告
- **合婚计算模块**：预留接口（待开发）

## 系统架构

```
八字占卜系统/
├── main.py                    # 主程序入口
├── config.json               # 配置文件
├── city_coordinates.json     # 城市经度数据
├── calendar_converter.py     # 阳历阴历换算模块
├── wuxing_calculator.py      # 五行计算模块
├── shishen_calculator.py     # 十神计算模块
├── dayun_calculator.py       # 流年大运计算模块
├── comprehensive_result.py   # 综合结果模块
└── README.md                 # 说明文档
```

## 安装依赖

```bash
pip install lunar-python
```

## 使用方法

### 1. 交互模式

直接运行主程序，按提示输入信息：

```bash
python main.py
```

系统会提示您输入：
- 姓名
- 性别（男/女）
- 出生年月日时分
- 出生城市

### 2. 测试模式

运行内置测试用例：

```bash
python main.py --test
```

### 3. API模式（预留）

系统预留了API接口，可供前端调用：

```python
from main import BaziSystem

system = BaziSystem()
result = system.run_api_mode({
    'name': '用户姓名',
    'gender': '男',
    'birth_year': 1990,
    'birth_month': 5,
    'birth_day': 15,
    'birth_hour': 14,
    'birth_minute': 30,
    'city_name': '广州'
})
```

## 配置说明

### config.json 配置文件

```json
{
  "藏干权重": {
    "本气": 1.0,
    "本气_中气": {"本气": 0.7, "中气": 0.3},
    "本气_中气_余气": {"本气": 0.6, "中气": 0.3, "余气": 0.1}
  },
  "五行计算参数": {
    "得令权重": 1.5,
    "失令权重": 0.5,
    "生扶系数": 0.2,
    "克制系数": 0.3
  },
  "地支关系影响系数": {
    "地支六冲": 2.0,
    "地支三合局": 1.8,
    "天干五合": 1.5,
    "地支相刑": 1.5,
    "地支六合": 1.2,
    "地支相害": 1.2,
    "普通生克": 1.0
  },
  "流年流月流日分析日期": {
    "年": 2025,
    "月": 10,
    "日": 1
  }
}
```

可以通过修改配置文件来调整各种计算参数。

## 输出示例

系统会输出完整的八字分析报告，包括：

```
==================================================
八字占卜系统 - 综合分析报告
==================================================

【基本信息】
姓名：测试用户
性别：男
出生地：广州 (经度: 113.2644°)
北京时间：1990年05月15日 14时30分
真太阳时：1990年05月15日 14时06分

【四柱八字】
年柱：庚午
月柱：辛巳
日柱：庚辰
时柱：癸未

【五行分析】
当前季节：夏
五行力量分布：
  木：0 (很弱, 平和)
  火：5.15 (过强, 得令)
  土：4.4 (过强, 平和)
  金：1.23 (偏弱, 失令)
  水：0.58 (很弱, 平和)

【十神分析】
日主：庚
身强身弱：身弱 (生扶力量: 2.80, 克泄耗力量: 3.10)
...
```

## 技术特点

1. **模块化设计**：每个功能模块独立，便于维护和扩展
2. **配置化参数**：所有计算参数都可通过配置文件调整
3. **精确计算**：使用lunar_python库确保农历转换的准确性
4. **真太阳时**：考虑地理位置和均时差的精确时间计算
5. **扩展性强**：预留API接口，支持前端集成

## 注意事项

1. 确保安装了lunar-python依赖库
2. 城市经度数据支持模糊匹配，如"广州"、"广州市"、"广东省"都能识别
3. 流年流月流日分析的目标日期可在配置文件中修改
4. 合婚功能暂未实现，已预留接口

## 开发说明

如需扩展功能或修改算法，请注意：

1. 所有计算参数都应放在config.json中，避免硬编码
2. 新增功能模块应遵循现有的模块化结构
3. 重要的计算逻辑变更应该在文档中明确标注
4. 测试新功能时可使用--test模式进行验证

## 版本信息

- 版本：1.0.0
- 开发日期：2024年
- Python版本要求：3.6+
- 主要依赖：lunar-python

## 联系方式

如有问题或建议，请联系开发团队。
