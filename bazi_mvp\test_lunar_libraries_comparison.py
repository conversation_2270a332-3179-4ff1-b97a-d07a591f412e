#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
农历转换库对比测试
对比不同农历转换库的输出结果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
from utils.calendar_converter import CalendarConverter

def test_zhdate():
    """测试zhdate库"""
    try:
        from zhdate import ZhDate
        return True, ZhDate
    except ImportError as e:
        return False, f"zhdate导入失败: {e}"

def test_chinese_lunar_calendar_converter():
    """测试chinese-lunar-calendar-converter库"""
    try:
        from chinese_lunar_calendar_converter import solar_to_lunar
        return True, solar_to_lunar
    except ImportError as e:
        return False, f"chinese-lunar-calendar-converter导入失败: {e}"

def test_lunar_python():
    """测试lunar-python库"""
    try:
        from lunar import Lunar
        return True, Lunar
    except ImportError as e:
        return False, f"lunar-python导入失败: {e}"

def test_lunardate():
    """测试lunardate库"""
    try:
        import lunardate
        return True, lunardate
    except ImportError as e:
        return False, f"lunardate导入失败: {e}"

def test_our_converter():
    """测试我们的自研转换器"""
    try:
        from utils.calendar_converter import CalendarConverter
        return True, CalendarConverter()
    except Exception as e:
        return False, f"自研转换器失败: {e}"

def convert_with_zhdate(zhdate_class, year, month, day):
    """使用zhdate转换"""
    try:
        solar_date = datetime(year, month, day)
        lunar_date = zhdate_class.from_datetime(solar_date)
        return {
            'lunar_year': lunar_date.year,
            'lunar_month': lunar_date.month,
            'lunar_day': lunar_date.day,
            'lunar_date_str': str(lunar_date),
            'is_leap': lunar_date.isleap,
            'library': 'zhdate'
        }
    except Exception as e:
        return {'error': f"zhdate转换失败: {e}", 'library': 'zhdate'}

def convert_with_chinese_lunar_calendar_converter(converter_func, year, month, day):
    """使用chinese-lunar-calendar-converter转换"""
    try:
        solar_date_str = f"{year}-{month:02d}-{day:02d}"
        lunar_info = converter_func(solar_date_str)
        return {
            'lunar_year': lunar_info[0],
            'lunar_month': lunar_info[1],
            'lunar_day': lunar_info[2],
            'lunar_date_str': f"{lunar_info[0]}年{lunar_info[1]}月{lunar_info[2]}日",
            'is_leap': False,  # 该库可能不提供闰月信息
            'library': 'chinese-lunar-calendar-converter'
        }
    except Exception as e:
        return {'error': f"chinese-lunar-calendar-converter转换失败: {e}", 'library': 'chinese-lunar-calendar-converter'}

def convert_with_lunar_python(lunar_class, year, month, day):
    """使用lunar-python转换"""
    try:
        solar_date = datetime(year, month, day)
        lunar = lunar_class.from_date(solar_date)
        return {
            'lunar_year': lunar.year,
            'lunar_month': lunar.month,
            'lunar_day': lunar.day,
            'lunar_date_str': str(lunar),
            'is_leap': lunar.is_leap,
            'library': 'lunar-python'
        }
    except Exception as e:
        return {'error': f"lunar-python转换失败: {e}", 'library': 'lunar-python'}

def convert_with_lunardate(lunardate_module, year, month, day):
    """使用lunardate转换"""
    try:
        solar_date = datetime(year, month, day)
        lunar_date = lunardate.LunarDate.from_datetime(solar_date)
        return {
            'lunar_year': lunar_date.year,
            'lunar_month': lunar_date.month,
            'lunar_day': lunar_date.day,
            'lunar_date_str': str(lunar_date),
            'is_leap': lunar_date.isleap,
            'library': 'lunardate'
        }
    except Exception as e:
        return {'error': f"lunardate转换失败: {e}", 'library': 'lunardate'}

def convert_with_our_converter(converter, year, month, day):
    """使用我们的转换器"""
    try:
        result = converter.solar_to_lunar(year, month, day)
        if result:
            return {
                'lunar_year': result['lunar_year'],
                'lunar_month': result['lunar_month'],
                'lunar_day': result['lunar_day'],
                'lunar_date_str': result['lunar_date_full'],
                'is_leap': result.get('is_leap_month', False),
                'accuracy': result.get('conversion_accuracy', 'unknown'),
                'library': '自研转换器'
            }
        else:
            return {'error': "自研转换器返回None", 'library': '自研转换器'}
    except Exception as e:
        return {'error': f"自研转换器失败: {e}", 'library': '自研转换器'}

def test_single_date(year, month, day, description=""):
    """测试单个日期的转换"""
    print(f"\n{'='*60}")
    print(f"测试日期: {year}年{month}月{day}日 {description}")
    print(f"{'='*60}")
    
    # 测试各个库
    results = []
    
    # 测试zhdate
    zhdate_available, zhdate_data = test_zhdate()
    if zhdate_available:
        result = convert_with_zhdate(zhdate_data, year, month, day)
        results.append(result)
        print(f"zhdate: {result}")
    else:
        print(f"zhdate: {zhdate_data}")
    
    # 测试chinese-lunar-calendar-converter
    converter_available, converter_data = test_chinese_lunar_calendar_converter()
    if converter_available:
        result = convert_with_chinese_lunar_calendar_converter(converter_data, year, month, day)
        results.append(result)
        print(f"chinese-lunar-calendar-converter: {result}")
    else:
        print(f"chinese-lunar-calendar-converter: {converter_data}")
    
    # 测试lunar-python
    lunar_python_available, lunar_python_data = test_lunar_python()
    if lunar_python_available:
        result = convert_with_lunar_python(lunar_python_data, year, month, day)
        results.append(result)
        print(f"lunar-python: {result}")
    else:
        print(f"lunar-python: {lunar_python_data}")
    
    # 测试lunardate
    lunardate_available, lunardate_data = test_lunardate()
    if lunardate_available:
        result = convert_with_lunardate(lunardate_data, year, month, day)
        results.append(result)
        print(f"lunardate: {result}")
    else:
        print(f"lunardate: {lunardate_data}")
    
    # 测试我们的转换器
    our_converter_available, our_converter_data = test_our_converter()
    if our_converter_available:
        result = convert_with_our_converter(our_converter_data, year, month, day)
        results.append(result)
        print(f"自研转换器: {result}")
    else:
        print(f"自研转换器: {our_converter_data}")
    
    # 分析结果
    print(f"\n📊 结果分析:")
    valid_results = [r for r in results if 'error' not in r]
    
    if len(valid_results) >= 2:
        # 比较农历年月日
        years = [r['lunar_year'] for r in valid_results]
        months = [r['lunar_month'] for r in valid_results]
        days = [r['lunar_day'] for r in valid_results]
        
        print(f"农历年份: {years} - {'一致' if len(set(years)) == 1 else '不一致'}")
        print(f"农历月份: {months} - {'一致' if len(set(months)) == 1 else '不一致'}")
        print(f"农历日期: {days} - {'一致' if len(set(days)) == 1 else '不一致'}")
        
        # 显示详细对比
        for i, result in enumerate(valid_results):
            print(f"  {result['library']}: {result['lunar_year']}年{result['lunar_month']}月{result['lunar_day']}日")
    else:
        print("有效结果不足，无法进行对比")

def main():
    """主测试函数"""
    print("🌙 农历转换库对比测试")
    print("=" * 60)
    
    # 测试日期列表
    test_dates = [
        (1990, 9, 2, "您提到的测试日期"),
        (1990, 1, 1, "1990年元旦"),
        (2000, 2, 5, "2000年春节"),
        (2023, 2, 14, "2023年情人节"),
        (2024, 1, 1, "2024年元旦"),
        (2024, 2, 10, "2024年春节"),
        (1995, 8, 20, "1995年8月20日"),
    ]
    
    for year, month, day, desc in test_dates:
        test_single_date(year, month, day, desc)
    
    print(f"\n{'='*60}")
    print("🎯 测试总结")
    print(f"{'='*60}")
    print("1. zhdate: 轻量级农历转换库，支持1900-2100年")
    print("2. chinese-lunar-calendar-converter: 基于天文计算的农历转换库")
    print("3. lunar-python: 功能丰富的农历转换库，支持多种历法")
    print("4. lunardate: 另一个农历转换库")
    print("5. 自研转换器: 项目自开发的转换器，有精确对照表")
    print("\n建议：")
    print("- 如果各库结果一致，说明转换准确")
    print("- 如果有差异，需要进一步验证哪个更准确")
    print("- 可以考虑使用第三方库替代自研实现")
    print("- lunar-python功能最全面，chinese-lunar-calendar-converter基于天文计算最准确")

if __name__ == "__main__":
    main()
