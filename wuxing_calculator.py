"""
五行计算模块
实现五行力量计算，包括藏干、旺度、生扶克制等计算
"""

import json


class WuxingCalculator:
    def __init__(self, config_path="config.json"):
        """初始化五行计算器"""
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        # 天干五行阴阳属性
        self.tiangan_info = {
            '甲': {'五行': '木', '阴阳': '阳', '编码': 1},
            '乙': {'五行': '木', '阴阳': '阴', '编码': 0},
            '丙': {'五行': '火', '阴阳': '阳', '编码': 1},
            '丁': {'五行': '火', '阴阳': '阴', '编码': 0},
            '戊': {'五行': '土', '阴阳': '阳', '编码': 1},
            '己': {'五行': '土', '阴阳': '阴', '编码': 0},
            '庚': {'五行': '金', '阴阳': '阳', '编码': 1},
            '辛': {'五行': '金', '阴阳': '阴', '编码': 0},
            '壬': {'五行': '水', '阴阳': '阳', '编码': 1},
            '癸': {'五行': '水', '阴阳': '阴', '编码': 0}
        }
        
        # 地支五行阴阳属性和藏干
        self.dizhi_info = {
            '子': {'五行': '水', '阴阳': '阳', '藏干': {'癸': 1.0}},
            '丑': {'五行': '土', '阴阳': '阴', '藏干': {'己': 0.7, '癸': 0.2, '辛': 0.1}},
            '寅': {'五行': '木', '阴阳': '阳', '藏干': {'甲': 0.6, '丙': 0.3, '戊': 0.1}},
            '卯': {'五行': '木', '阴阳': '阴', '藏干': {'乙': 1.0}},
            '辰': {'五行': '土', '阴阳': '阳', '藏干': {'戊': 0.6, '乙': 0.3, '癸': 0.1}},
            '巳': {'五行': '火', '阴阳': '阴', '藏干': {'丙': 0.6, '戊': 0.3, '庚': 0.1}},
            '午': {'五行': '火', '阴阳': '阳', '藏干': {'丁': 0.7, '己': 0.3}},
            '未': {'五行': '土', '阴阳': '阴', '藏干': {'己': 0.6, '丁': 0.3, '乙': 0.1}},
            '申': {'五行': '金', '阴阳': '阳', '藏干': {'庚': 0.6, '壬': 0.3, '戊': 0.1}},
            '酉': {'五行': '金', '阴阳': '阴', '藏干': {'辛': 1.0}},
            '戌': {'五行': '土', '阴阳': '阳', '藏干': {'戊': 0.6, '辛': 0.3, '丁': 0.1}},
            '亥': {'五行': '水', '阴阳': '阴', '藏干': {'壬': 0.7, '甲': 0.3}}
        }
        
        # 五行相生相克关系
        self.wuxing_sheng = {'木': '火', '火': '土', '土': '金', '金': '水', '水': '木'}
        self.wuxing_ke = {'木': '土', '土': '水', '水': '火', '火': '金', '金': '木'}
        
        # 季节对应的五行旺度
        self.season_wangdu = {
            '春': {'旺': '木', '相': '火', '死': '土', '囚': '金', '休': '水'},
            '夏': {'旺': '火', '相': '土', '死': '金', '囚': '水', '休': '木'},
            '秋': {'旺': '金', '相': '水', '死': '木', '囚': '火', '休': '土'},
            '冬': {'旺': '水', '相': '木', '死': '火', '囚': '土', '休': '金'},
            '季末': {'旺': '土', '相': '金', '死': '水', '囚': '木', '休': '火'}
        }
    
    def get_season_from_month(self, month_zhi):
        """根据月支确定季节"""
        season_map = {
            '寅': '春', '卯': '春', '辰': '春',
            '巳': '夏', '午': '夏', '未': '夏', 
            '申': '秋', '酉': '秋', '戌': '秋',
            '亥': '冬', '子': '冬', '丑': '冬'
        }
        
        # 辰、未、戌、丑为季末土
        if month_zhi in ['辰', '未', '戌', '丑']:
            return '季末'
        
        return season_map.get(month_zhi, '春')
    
    def calculate_basic_wuxing_count(self, sizhu):
        """计算基础五行数量"""
        wuxing_count = {'木': 0, '火': 0, '土': 0, '金': 0, '水': 0}
        
        # 统计天干
        for gan in [sizhu['年柱'][0], sizhu['月柱'][0], sizhu['日柱'][0], sizhu['时柱'][0]]:
            wuxing = self.tiangan_info[gan]['五行']
            wuxing_count[wuxing] += 1
        
        # 统计地支本气
        for zhi in [sizhu['年柱'][1], sizhu['月柱'][1], sizhu['日柱'][1], sizhu['时柱'][1]]:
            wuxing = self.dizhi_info[zhi]['五行']
            wuxing_count[wuxing] += 1
        
        return wuxing_count
    
    def calculate_canggan_contribution(self, sizhu):
        """计算藏干对五行的贡献"""
        canggan_contribution = {'木': 0, '火': 0, '土': 0, '金': 0, '水': 0}
        
        for zhi in [sizhu['年柱'][1], sizhu['月柱'][1], sizhu['日柱'][1], sizhu['时柱'][1]]:
            canggan_dict = self.dizhi_info[zhi]['藏干']
            
            for gan, weight in canggan_dict.items():
                wuxing = self.tiangan_info[gan]['五行']
                canggan_contribution[wuxing] += weight
        
        return canggan_contribution
    
    def calculate_wuxing_scores(self, sizhu, lunar_info):
        """计算五行力量分数"""
        # 获取月支确定季节
        month_zhi = sizhu['月柱'][1]
        season = self.get_season_from_month(month_zhi)
        season_wangdu = self.season_wangdu[season]
        
        # 基础五行计数
        basic_count = self.calculate_basic_wuxing_count(sizhu)
        
        # 藏干贡献
        canggan_contrib = self.calculate_canggan_contribution(sizhu)
        
        # 获取配置参数
        deling_weight = self.config['五行计算参数']['得令权重']
        shiling_weight = self.config['五行计算参数']['失令权重']
        shengfu_coeff = self.config['五行计算参数']['生扶系数']
        kezhi_coeff = self.config['五行计算参数']['克制系数']
        
        # 计算每个五行的最终分数
        final_scores = {}
        
        for wuxing in ['木', '火', '土', '金', '水']:
            # 1. 基础分数 = 基础计数 + 藏干贡献
            base_score = basic_count[wuxing] + canggan_contrib[wuxing]
            
            # 2. 应用季节增益/减益
            if wuxing == season_wangdu['旺']:
                # 得令
                score = base_score * deling_weight
                status = '得令'
            elif wuxing == season_wangdu['死']:
                # 失令
                score = base_score * shiling_weight
                status = '失令'
            else:
                score = base_score
                status = '平和'
            
            # 3. 应用生扶增益
            sheng_wuxing = None
            for wx, sheng_target in self.wuxing_sheng.items():
                if sheng_target == wuxing:
                    sheng_wuxing = wx
                    break
            
            if sheng_wuxing:
                sheng_score = basic_count[sheng_wuxing] + canggan_contrib[sheng_wuxing]
                if sheng_score > 0:
                    shengfu_bonus = sheng_score * shengfu_coeff
                    score += shengfu_bonus
            
            # 4. 应用克制减益
            ke_wuxing = None
            for wx, ke_target in self.wuxing_ke.items():
                if ke_target == wuxing:
                    ke_wuxing = wx
                    break
            
            if ke_wuxing:
                ke_score = basic_count[ke_wuxing] + canggan_contrib[ke_wuxing]
                if ke_score > 0:
                    kezhi_penalty = ke_score * kezhi_coeff
                    score -= kezhi_penalty
            
            # 确保分数不为负
            score = max(0, score)
            
            # 判断强弱状态
            if score >= 4:
                strength_status = '过强'
            elif score >= 2.5:
                strength_status = '中和偏强'
            elif score >= 1.5:
                strength_status = '中和'
            elif score >= 0.8:
                strength_status = '偏弱'
            else:
                strength_status = '很弱'
            
            final_scores[wuxing] = {
                '力量值': round(score, 2),
                '状态': strength_status,
                '季节状态': status,
                '基础分数': round(base_score, 2)
            }
        
        return final_scores, season
    
    def format_wuxing_output(self, wuxing_scores, season):
        """格式化五行输出结果"""
        output = f"""
=== 五行力量分析结果 ===
当前季节：{season}

五行力量分布：
"""
        for wuxing, info in wuxing_scores.items():
            output += f"{wuxing}：{info['力量值']} ({info['状态']}, {info['季节状态']})\n"
        
        return output


# 测试函数
def test_wuxing_calculator():
    """测试五行计算模块"""
    from calendar_converter import CalendarConverter
    
    # 先获取四柱信息
    converter = CalendarConverter()
    result = converter.process_birth_info(
        birth_year=1990,
        birth_month=5,
        birth_day=15,
        birth_hour=14,
        birth_minute=30,
        city_name="广州",
        name="测试用户"
    )
    
    # 计算五行
    calculator = WuxingCalculator()
    wuxing_scores, season = calculator.calculate_wuxing_scores(
        result['四柱'], 
        result['阴历信息']
    )
    
    print(calculator.format_wuxing_output(wuxing_scores, season))
    return wuxing_scores


if __name__ == "__main__":
    test_wuxing_calculator()
