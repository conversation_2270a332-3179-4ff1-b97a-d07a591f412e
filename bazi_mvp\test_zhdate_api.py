#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试zhdate库的正确API用法
"""

from zhdate import ZhDate
from datetime import datetime

def test_zhdate_api():
    """测试zhdate库的API"""
    print("🔍 测试zhdate库API")
    print("=" * 40)
    
    # 测试日期
    solar_date = datetime(1990, 9, 2)
    lunar_date = ZhDate.from_datetime(solar_date)
    
    print(f"阳历日期: {solar_date}")
    print(f"农历日期对象: {lunar_date}")
    print(f"农历日期字符串: {str(lunar_date)}")
    
    # 检查对象属性
    print(f"\n对象属性:")
    attrs = [attr for attr in dir(lunar_date) if not attr.startswith('_')]
    for attr in attrs:
        try:
            value = getattr(lunar_date, attr)
            if not callable(value):
                print(f"  {attr}: {value}")
        except:
            pass
    
    # 尝试获取年月日
    print(f"\n尝试获取年月日:")
    try:
        print(f"  lunar_date.year: {lunar_date.year}")
    except Exception as e:
        print(f"  lunar_date.year: 错误 - {e}")
    
    try:
        print(f"  lunar_date.month: {lunar_date.month}")
    except Exception as e:
        print(f"  lunar_date.month: 错误 - {e}")
    
    try:
        print(f"  lunar_date.day: {lunar_date.day}")
    except Exception as e:
        print(f"  lunar_date.day: 错误 - {e}")
    
    # 尝试其他方法
    print(f"\n尝试其他方法:")
    try:
        print(f"  lunar_date.lunar_year: {lunar_date.lunar_year}")
    except Exception as e:
        print(f"  lunar_date.lunar_year: 错误 - {e}")
    
    try:
        print(f"  lunar_date.lunar_month: {lunar_date.lunar_month}")
    except Exception as e:
        print(f"  lunar_date.lunar_month: 错误 - {e}")
    
    try:
        print(f"  lunar_date.lunar_day: {lunar_date.lunar_day}")
    except Exception as e:
        print(f"  lunar_date.lunar_day: 错误 - {e}")

if __name__ == "__main__":
    test_zhdate_api()

