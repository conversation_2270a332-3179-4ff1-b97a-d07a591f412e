"""
测试真太阳时计算的不同方法
"""

import math
from datetime import datetime, timedelta


def method1_original(beijing_time, longitude):
    """我们原来的方法"""
    # 经度时差：(当地经度−120°)×4分钟
    longitude_diff_minutes = (longitude - 120) * 4
    
    # 计算一年中的第几天
    day_of_year = beijing_time.timetuple().tm_yday
    
    # 计算日角 d (以弧度为单位)
    d = 2 * math.pi * (day_of_year - 1) / 365
    
    # 均时差计算
    eot_minutes = 229.2 * (
        0.000075 + 
        0.001868 * math.cos(d) - 
        0.032077 * math.sin(d) - 
        0.014615 * math.cos(2 * d) - 
        0.04089 * math.sin(2 * d)
    )
    
    # 真太阳时 = 北京时间 + 经度时差 + 均时差
    total_correction_minutes = longitude_diff_minutes + eot_minutes
    true_solar_time = beijing_time + timedelta(minutes=total_correction_minutes)
    
    return true_solar_time, longitude_diff_minutes, eot_minutes


def method2_csdn(beijing_time, longitude):
    """基于CSDN博客的方法"""
    # 计算一年中的第几天
    day_of_year = beijing_time.timetuple().tm_yday
    
    # 计算B (以度为单位)
    B = (day_of_year - 1) * 360 / 365
    
    # 均时差计算 (以分钟为单位)
    E = 229.2 * (
        0.000075 + 
        0.001868 * math.cos(math.radians(B)) - 
        0.032077 * math.sin(math.radians(B)) - 
        0.014615 * math.cos(math.radians(2 * B)) - 
        0.04089 * math.sin(math.radians(2 * B))
    )
    
    # 经度时差 (Lst是标准子午线，Lloc是当地经度)
    # 对于北京时间，Lst = 120°
    # 对于东经，当地经度直接使用
    Lst = 120  # 北京时间标准子午线
    Lloc = longitude  # 当地经度
    
    # 太阳时 - 标准时 = 4*(Lst - Lloc) + E
    # 但这里的公式可能有问题，让我们试试相反的符号
    difference_minutes = 4 * (Lst - Lloc) + E
    
    true_solar_time = beijing_time + timedelta(minutes=difference_minutes)
    
    return true_solar_time, 4 * (Lst - Lloc), E


def method3_reverse_longitude(beijing_time, longitude):
    """尝试相反的经度时差计算"""
    # 经度时差：(120° - 当地经度)×4分钟 (相反方向)
    longitude_diff_minutes = (120 - longitude) * 4
    
    # 计算一年中的第几天
    day_of_year = beijing_time.timetuple().tm_yday
    
    # 计算日角 d (以弧度为单位)
    d = 2 * math.pi * (day_of_year - 1) / 365
    
    # 均时差计算
    eot_minutes = 229.2 * (
        0.000075 + 
        0.001868 * math.cos(d) - 
        0.032077 * math.sin(d) - 
        0.014615 * math.cos(2 * d) - 
        0.04089 * math.sin(2 * d)
    )
    
    # 真太阳时 = 北京时间 + 经度时差 + 均时差
    total_correction_minutes = longitude_diff_minutes + eot_minutes
    true_solar_time = beijing_time + timedelta(minutes=total_correction_minutes)
    
    return true_solar_time, longitude_diff_minutes, eot_minutes


def method4_reverse_eot(beijing_time, longitude):
    """尝试相反的均时差符号"""
    # 经度时差：(当地经度−120°)×4分钟
    longitude_diff_minutes = (longitude - 120) * 4
    
    # 计算一年中的第几天
    day_of_year = beijing_time.timetuple().tm_yday
    
    # 计算日角 d (以弧度为单位)
    d = 2 * math.pi * (day_of_year - 1) / 365
    
    # 均时差计算 (相反符号)
    eot_minutes = -229.2 * (
        0.000075 + 
        0.001868 * math.cos(d) - 
        0.032077 * math.sin(d) - 
        0.014615 * math.cos(2 * d) - 
        0.04089 * math.sin(2 * d)
    )
    
    # 真太阳时 = 北京时间 + 经度时差 + 均时差
    total_correction_minutes = longitude_diff_minutes + eot_minutes
    true_solar_time = beijing_time + timedelta(minutes=total_correction_minutes)
    
    return true_solar_time, longitude_diff_minutes, eot_minutes


def method5_spencer_formula(beijing_time, longitude):
    """使用Spencer更精确的均时差公式"""
    # 经度时差：(120°−当地经度)×4分钟
    longitude_diff_minutes = (120 - longitude) * 4

    # 计算一年中的第几天
    day_of_year = beijing_time.timetuple().tm_yday

    # Spencer公式中的Γ (gamma)
    gamma = 2 * math.pi * (day_of_year - 1) / 365.25  # 使用365.25更精确

    # Spencer的均时差公式 (以分钟为单位)
    eot_minutes = 229.18 * (
        0.000075 +
        0.001868 * math.cos(gamma) -
        0.032077 * math.sin(gamma) -
        0.014615 * math.cos(2 * gamma) -
        0.040849 * math.sin(2 * gamma)
    )

    # 真太阳时 = 北京时间 + 经度时差 + 均时差
    total_correction_minutes = longitude_diff_minutes + eot_minutes
    true_solar_time = beijing_time + timedelta(minutes=total_correction_minutes)

    return true_solar_time, longitude_diff_minutes, eot_minutes


def method6_different_day_calc(beijing_time, longitude):
    """尝试不同的日期计算方法"""
    # 经度时差：(120°−当地经度)×4分钟
    longitude_diff_minutes = (120 - longitude) * 4

    # 计算从1月1日开始的天数 (不减1)
    day_of_year = beijing_time.timetuple().tm_yday

    # 使用不同的日角计算
    B = (day_of_year) * 360 / 365  # 不减1

    # 均时差计算
    eot_minutes = 229.2 * (
        0.000075 +
        0.001868 * math.cos(math.radians(B)) -
        0.032077 * math.sin(math.radians(B)) -
        0.014615 * math.cos(math.radians(2 * B)) -
        0.04089 * math.sin(math.radians(2 * B))
    )

    # 真太阳时 = 北京时间 + 经度时差 + 均时差
    total_correction_minutes = longitude_diff_minutes + eot_minutes
    true_solar_time = beijing_time + timedelta(minutes=total_correction_minutes)

    return true_solar_time, longitude_diff_minutes, eot_minutes


def method7_local_mean_time_first(beijing_time, longitude):
    """先计算地方平太阳时，再加均时差"""
    # 第一步：北京时间 -> 地方平太阳时
    # 地方平太阳时 = 北京时间 + 经度时差
    longitude_diff_minutes = (longitude - 120) * 4  # 注意这里用原来的方向
    local_mean_time = beijing_time + timedelta(minutes=longitude_diff_minutes)

    # 第二步：计算均时差
    day_of_year = beijing_time.timetuple().tm_yday
    B = (day_of_year - 1) * 360 / 365

    eot_minutes = 229.2 * (
        0.000075 +
        0.001868 * math.cos(math.radians(B)) -
        0.032077 * math.sin(math.radians(B)) -
        0.014615 * math.cos(math.radians(2 * B)) -
        0.04089 * math.sin(math.radians(2 * B))
    )

    # 第三步：地方平太阳时 + 均时差 = 真太阳时
    true_solar_time = local_mean_time + timedelta(minutes=eot_minutes)

    return true_solar_time, longitude_diff_minutes, eot_minutes


def method8_try_larger_eot(beijing_time, longitude):
    """尝试更大的均时差值"""
    # 经度时差：(120°−当地经度)×4分钟
    longitude_diff_minutes = (120 - longitude) * 4

    # 计算一年中的第几天
    day_of_year = beijing_time.timetuple().tm_yday

    # 计算日角 d (以弧度为单位)
    d = 2 * math.pi * (day_of_year - 1) / 365

    # 尝试放大均时差 (可能我们的公式系数有问题)
    eot_minutes = 229.2 * (
        0.000075 +
        0.001868 * math.cos(d) -
        0.032077 * math.sin(d) -
        0.014615 * math.cos(2 * d) -
        0.04089 * math.sin(2 * d)
    )

    # 尝试手动调整均时差，看看是否能达到目标
    # 目标是15:36，当前是15:00，差36分钟
    # 当前均时差约4分钟，需要增加32分钟
    adjusted_eot = eot_minutes + 32  # 手动调整

    # 真太阳时 = 北京时间 + 经度时差 + 调整后的均时差
    total_correction_minutes = longitude_diff_minutes + adjusted_eot
    true_solar_time = beijing_time + timedelta(minutes=total_correction_minutes)

    return true_solar_time, longitude_diff_minutes, adjusted_eot


def test_all_methods():
    """测试所有方法"""
    # 测试数据：1990年5月15日14时30分，广州
    beijing_time = datetime(1990, 5, 15, 14, 30)
    longitude = 113.2644  # 广州经度

    print("测试真太阳时计算的不同方法")
    print(f"北京时间: {beijing_time.strftime('%Y年%m月%d日 %H时%M分')}")
    print(f"广州经度: {longitude}°")
    print(f"目标结果: 15时36分")
    print(f"当前最佳结果: 15时00分 (差距36分钟)")
    print("-" * 50)
    
    methods = [
        ("方法1 - 原始方法", method1_original),
        ("方法2 - CSDN方法", method2_csdn),
        ("方法3 - 反向经度时差", method3_reverse_longitude),
        ("方法4 - 反向均时差", method4_reverse_eot),
        ("方法5 - Spencer精确公式", method5_spencer_formula),
        ("方法6 - 不同日期计算", method6_different_day_calc),
        ("方法7 - 先算地方平太阳时", method7_local_mean_time_first),
        ("方法8 - 手动调整均时差", method8_try_larger_eot)
    ]
    
    for name, method in methods:
        try:
            result, long_diff, eot = method(beijing_time, longitude)
            print(f"{name}:")
            print(f"  真太阳时: {result.strftime('%H时%M分')}")
            print(f"  经度时差: {long_diff:.2f}分钟")
            print(f"  均时差: {eot:.2f}分钟")
            print(f"  总修正: {long_diff + eot:.2f}分钟")
            print()
        except Exception as e:
            print(f"{name}: 计算错误 - {e}")
            print()


if __name__ == "__main__":
    test_all_methods()
