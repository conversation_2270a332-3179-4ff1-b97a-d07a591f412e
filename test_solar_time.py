"""
测试真太阳时计算的不同方法
"""

import math
from datetime import datetime, timedelta


def method1_original(beijing_time, longitude):
    """我们原来的方法"""
    # 经度时差：(当地经度−120°)×4分钟
    longitude_diff_minutes = (longitude - 120) * 4
    
    # 计算一年中的第几天
    day_of_year = beijing_time.timetuple().tm_yday
    
    # 计算日角 d (以弧度为单位)
    d = 2 * math.pi * (day_of_year - 1) / 365
    
    # 均时差计算
    eot_minutes = 229.2 * (
        0.000075 + 
        0.001868 * math.cos(d) - 
        0.032077 * math.sin(d) - 
        0.014615 * math.cos(2 * d) - 
        0.04089 * math.sin(2 * d)
    )
    
    # 真太阳时 = 北京时间 + 经度时差 + 均时差
    total_correction_minutes = longitude_diff_minutes + eot_minutes
    true_solar_time = beijing_time + timedelta(minutes=total_correction_minutes)
    
    return true_solar_time, longitude_diff_minutes, eot_minutes


def method2_csdn(beijing_time, longitude):
    """基于CSDN博客的方法"""
    # 计算一年中的第几天
    day_of_year = beijing_time.timetuple().tm_yday
    
    # 计算B (以度为单位)
    B = (day_of_year - 1) * 360 / 365
    
    # 均时差计算 (以分钟为单位)
    E = 229.2 * (
        0.000075 + 
        0.001868 * math.cos(math.radians(B)) - 
        0.032077 * math.sin(math.radians(B)) - 
        0.014615 * math.cos(math.radians(2 * B)) - 
        0.04089 * math.sin(math.radians(2 * B))
    )
    
    # 经度时差 (Lst是标准子午线，Lloc是当地经度)
    # 对于北京时间，Lst = 120°
    # 对于东经，当地经度直接使用
    Lst = 120  # 北京时间标准子午线
    Lloc = longitude  # 当地经度
    
    # 太阳时 - 标准时 = 4*(Lst - Lloc) + E
    # 但这里的公式可能有问题，让我们试试相反的符号
    difference_minutes = 4 * (Lst - Lloc) + E
    
    true_solar_time = beijing_time + timedelta(minutes=difference_minutes)
    
    return true_solar_time, 4 * (Lst - Lloc), E


def method3_reverse_longitude(beijing_time, longitude):
    """尝试相反的经度时差计算"""
    # 经度时差：(120° - 当地经度)×4分钟 (相反方向)
    longitude_diff_minutes = (120 - longitude) * 4
    
    # 计算一年中的第几天
    day_of_year = beijing_time.timetuple().tm_yday
    
    # 计算日角 d (以弧度为单位)
    d = 2 * math.pi * (day_of_year - 1) / 365
    
    # 均时差计算
    eot_minutes = 229.2 * (
        0.000075 + 
        0.001868 * math.cos(d) - 
        0.032077 * math.sin(d) - 
        0.014615 * math.cos(2 * d) - 
        0.04089 * math.sin(2 * d)
    )
    
    # 真太阳时 = 北京时间 + 经度时差 + 均时差
    total_correction_minutes = longitude_diff_minutes + eot_minutes
    true_solar_time = beijing_time + timedelta(minutes=total_correction_minutes)
    
    return true_solar_time, longitude_diff_minutes, eot_minutes


def method4_reverse_eot(beijing_time, longitude):
    """尝试相反的均时差符号"""
    # 经度时差：(当地经度−120°)×4分钟
    longitude_diff_minutes = (longitude - 120) * 4
    
    # 计算一年中的第几天
    day_of_year = beijing_time.timetuple().tm_yday
    
    # 计算日角 d (以弧度为单位)
    d = 2 * math.pi * (day_of_year - 1) / 365
    
    # 均时差计算 (相反符号)
    eot_minutes = -229.2 * (
        0.000075 + 
        0.001868 * math.cos(d) - 
        0.032077 * math.sin(d) - 
        0.014615 * math.cos(2 * d) - 
        0.04089 * math.sin(2 * d)
    )
    
    # 真太阳时 = 北京时间 + 经度时差 + 均时差
    total_correction_minutes = longitude_diff_minutes + eot_minutes
    true_solar_time = beijing_time + timedelta(minutes=total_correction_minutes)
    
    return true_solar_time, longitude_diff_minutes, eot_minutes


def test_all_methods():
    """测试所有方法"""
    # 测试数据：1990年5月15日14时30分，广州
    beijing_time = datetime(1990, 5, 15, 14, 30)
    longitude = 113.2644  # 广州经度
    
    print("测试真太阳时计算的不同方法")
    print(f"北京时间: {beijing_time.strftime('%Y年%m月%d日 %H时%M分')}")
    print(f"广州经度: {longitude}°")
    print(f"目标结果: 15时36分")
    print("-" * 50)
    
    methods = [
        ("方法1 - 原始方法", method1_original),
        ("方法2 - CSDN方法", method2_csdn),
        ("方法3 - 反向经度时差", method3_reverse_longitude),
        ("方法4 - 反向均时差", method4_reverse_eot)
    ]
    
    for name, method in methods:
        try:
            result, long_diff, eot = method(beijing_time, longitude)
            print(f"{name}:")
            print(f"  真太阳时: {result.strftime('%H时%M分')}")
            print(f"  经度时差: {long_diff:.2f}分钟")
            print(f"  均时差: {eot:.2f}分钟")
            print(f"  总修正: {long_diff + eot:.2f}分钟")
            print()
        except Exception as e:
            print(f"{name}: 计算错误 - {e}")
            print()


if __name__ == "__main__":
    test_all_methods()
