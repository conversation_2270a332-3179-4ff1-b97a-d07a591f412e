"""
测试天干五合化成功的具体案例
"""

from comprehensive_result import ComprehensiveAnalyzer

def test_hehua_success():
    """测试天干五合化成功的计算"""
    analyzer = ComprehensiveAnalyzer()
    
    # 创建一个包含天干五合化成功的测试案例
    result = analyzer.analyze_complete_bazi(
        birth_year=1985,  # 乙丑年
        birth_month=3,
        birth_day=15,
        birth_hour=15,
        birth_minute=0,
        city_name="广州",
        name="合化测试",
        gender="男"
    )
    
    # 检查结果中是否有天干五合化成功的计算
    print("=== 检查天干五合化成功计算 ===")
    
    # 检查流年计算详情
    if '流年计算详情' in result['大运流年']:
        liunian_details = result['大运流年']['流年计算详情']
        if '第四步_流年与原局作用关系' in liunian_details:
            tiangan_zuoyong = liunian_details['第四步_流年与原局作用关系']['天干作用详情']
            
            print("天干作用详情：")
            for zuoyong in tiangan_zuoyong:
                print(f"  {zuoyong['原局']} - {zuoyong['五行关系']}")
                if '计算详情' in zuoyong:
                    print(f"    计算详情: {zuoyong['计算详情']}")
                print(f"    计算公式: {zuoyong['计算公式']}")
                print(f"    得分: {zuoyong['得分']}")
                print()
    
    # 保存详细结果用于检查
    detailed_result = analyzer.format_comprehensive_output(result, show_details=True)
    with open("天干五合化成功测试_详细过程.txt", "w", encoding="utf-8") as f:
        f.write(detailed_result)
    
    print("详细结果已保存到: 天干五合化成功测试_详细过程.txt")

if __name__ == "__main__":
    test_hehua_success()
