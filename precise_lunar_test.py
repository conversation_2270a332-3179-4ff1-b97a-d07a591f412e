#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确时间lunar_python测试
测试是否正确处理小时分钟和节气交替
"""

import sys
import os

def test_precise_time():
    """测试精确时间处理"""
    print("🕐 lunar_python 精确时间测试")
    print("=" * 60)
    
    try:
        # 导入lunar_python库
        from lunar_python import Solar
        
        print("✅ lunar_python 库加载成功")
        
        # 测试1969-02-04的两个时间点（立春节气）
        test_cases = [
            (1969, 2, 4, 6, 0, 0, "1969-02-04 06:00:00 (立春前)"),
            (1969, 2, 4, 18, 0, 0, "1969-02-04 18:00:00 (立春后)"),
            (2020, 4, 4, 6, 0, 0, "2020-04-04 06:00:00 (清明前)"),
            (2020, 4, 4, 18, 0, 0, "2020-04-04 18:00:00 (清明后)")
        ]
        
        print("\n🧪 精确时间测试:")
        
        for year, month, day, hour, minute, second, desc in test_cases:
            print(f"\n📅 {desc}")
            print("-" * 50)
            
            try:
                # 使用精确时间方法
                solar = Solar.fromYmdHms(year, month, day, hour, minute, second)
                lunar = solar.getLunar()
                
                print(f"阳历: {solar.toString()}")
                print(f"农历: {lunar.toString()}")
                
                # 获取八字
                eightChar = lunar.getEightChar()
                
                print(f"年柱: {eightChar.getYear()}")
                print(f"月柱: {eightChar.getMonth()}")
                print(f"日柱: {eightChar.getDay()}")
                print(f"时柱: {eightChar.getTime()}")
                
                # 检查节气
                try:
                    jieqi = solar.getJieQi()
                    if jieqi:
                        print(f"节气: {jieqi}")
                except:
                    pass
                
            except Exception as e:
                print(f"❌ 转换失败: {e}")
        
        # 对比测试：使用fromYmd vs fromYmdHms
        print("\n" + "="*60)
        print("🔍 对比测试: fromYmd vs fromYmdHms")
        
        test_date = (1969, 2, 4)
        
        print(f"\n📅 测试日期: {test_date[0]}-{test_date[1]:02d}-{test_date[2]:02d}")
        print("-" * 50)
        
        # 方法1: 只有日期 (fromYmd)
        print("方法1: Solar.fromYmd() - 默认00:00:00")
        try:
            solar1 = Solar.fromYmd(*test_date)
            lunar1 = solar1.getLunar()
            eightChar1 = lunar1.getEightChar()
            
            print(f"阳历: {solar1.toString()}")
            print(f"农历: {lunar1.toString()}")
            print(f"年柱: {eightChar1.getYear()}")
            print(f"月柱: {eightChar1.getMonth()}")
            print(f"日柱: {eightChar1.getDay()}")
            print(f"时柱: {eightChar1.getTime()}")
        except Exception as e:
            print(f"❌ 失败: {e}")
        
        # 方法2: 精确时间 (fromYmdHms) - 6:00
        print("\n方法2: Solar.fromYmdHms() - 06:00:00")
        try:
            solar2 = Solar.fromYmdHms(test_date[0], test_date[1], test_date[2], 6, 0, 0)
            lunar2 = solar2.getLunar()
            eightChar2 = lunar2.getEightChar()
            
            print(f"阳历: {solar2.toString()}")
            print(f"农历: {lunar2.toString()}")
            print(f"年柱: {eightChar2.getYear()}")
            print(f"月柱: {eightChar2.getMonth()}")
            print(f"日柱: {eightChar2.getDay()}")
            print(f"时柱: {eightChar2.getTime()}")
        except Exception as e:
            print(f"❌ 失败: {e}")
        
        # 方法3: 精确时间 (fromYmdHms) - 18:00
        print("\n方法3: Solar.fromYmdHms() - 18:00:00")
        try:
            solar3 = Solar.fromYmdHms(test_date[0], test_date[1], test_date[2], 18, 0, 0)
            lunar3 = solar3.getLunar()
            eightChar3 = lunar3.getEightChar()
            
            print(f"阳历: {solar3.toString()}")
            print(f"农历: {lunar3.toString()}")
            print(f"年柱: {eightChar3.getYear()}")
            print(f"月柱: {eightChar3.getMonth()}")
            print(f"日柱: {eightChar3.getDay()}")
            print(f"时柱: {eightChar3.getTime()}")
        except Exception as e:
            print(f"❌ 失败: {e}")
        
        # 交互模式
        print("\n" + "="*60)
        print("🔄 精确时间交互模式")
        
        while True:
            try:
                user_input = input("\n请输入日期 (YYYY-MM-DD) 或 'quit' 退出: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                # 解析日期
                date_parts = user_input.split('-')
                if len(date_parts) != 3:
                    print("❌ 格式错误，请使用 YYYY-MM-DD")
                    continue
                
                year, month, day = map(int, date_parts)
                
                # 询问精确时间
                time_input = input("请输入时间 (HH:MM，默认12:00): ").strip()
                if time_input:
                    time_parts = time_input.split(':')
                    hour = int(time_parts[0])
                    minute = int(time_parts[1]) if len(time_parts) > 1 else 0
                else:
                    hour, minute = 12, 0
                
                print(f"\n🌟 精确转换结果: {year}年{month}月{day}日 {hour:02d}:{minute:02d}")
                print("=" * 50)
                
                # 使用精确时间方法
                solar = Solar.fromYmdHms(year, month, day, hour, minute, 0)
                lunar = solar.getLunar()
                
                print(f"📅 阳历: {solar.toString()}")
                print(f"📅 农历: {lunar.toString()}")
                
                # 获取八字
                eightChar = lunar.getEightChar()
                
                print(f"\n🎯 四柱八字:")
                print(f"年柱: {eightChar.getYear()}")
                print(f"月柱: {eightChar.getMonth()}")
                print(f"日柱: {eightChar.getDay()}")
                print(f"时柱: {eightChar.getTime()}")
                
            except ValueError:
                print("❌ 日期或时间格式错误")
            except KeyboardInterrupt:
                print("\n👋 程序被中断，再见！")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ lunar_python 库导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    # 确保当前目录在Python路径中
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # 运行精确时间测试
    test_precise_time()

if __name__ == "__main__":
    main()
