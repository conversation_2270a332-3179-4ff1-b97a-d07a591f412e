"""
测试所有天干五合的计算
"""

from comprehensive_result import ComprehensiveAnalyzer

def test_all_wuhe():
    """测试所有天干五合的计算"""
    analyzer = ComprehensiveAnalyzer()
    
    # 测试多个不同的八字，寻找天干五合的情况
    test_cases = [
        {"year": 1985, "month": 3, "day": 15, "hour": 15, "name": "测试1"},
        {"year": 1984, "month": 5, "day": 15, "hour": 15, "name": "测试2"},
        {"year": 1986, "month": 7, "day": 20, "hour": 10, "name": "测试3"},
        {"year": 1987, "month": 9, "day": 25, "hour": 14, "name": "测试4"},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n=== 测试案例 {i+1}: {case['name']} ===")
        
        result = analyzer.analyze_complete_bazi(
            birth_year=case["year"],
            birth_month=case["month"],
            birth_day=case["day"],
            birth_hour=case["hour"],
            birth_minute=0,
            city_name="广州",
            name=case["name"],
            gender="男"
        )
        
        # 检查流年计算详情
        if '流年计算详情' in result['大运流年']:
            liunian_details = result['大运流年']['流年计算详情']
            if '第四步_流年与原局作用关系' in liunian_details:
                tiangan_zuoyong = liunian_details['第四步_流年与原局作用关系']['天干作用详情']
                
                # 查找天干五合的情况
                wuhe_found = False
                for zuoyong in tiangan_zuoyong:
                    if '天干五合' in zuoyong['五行关系']:
                        wuhe_found = True
                        print(f"  发现天干五合: {zuoyong['原局']} - {zuoyong['五行关系']}")
                        print(f"    十神: {zuoyong['十神']}")
                        if '计算详情' in zuoyong:
                            print(f"    计算详情: {zuoyong['计算详情']}")
                        print(f"    计算公式: {zuoyong['计算公式']}")
                        print(f"    得分: {zuoyong['得分']}")
                        
                        # 检查是否有错误的格式
                        if '+ 1 + 1' in str(zuoyong['计算公式']):
                            print(f"    ❌ 发现错误格式: {zuoyong['计算公式']}")
                        else:
                            print(f"    ✅ 格式正确")
                        print()
                
                if not wuhe_found:
                    print("  未发现天干五合")
        
        # 检查流月计算详情
        if '流月计算详情' in result['大运流年']:
            liuyue_details = result['大运流年']['流月计算详情']
            if '第二步_流月与各方作用分析' in liuyue_details:
                tiangan_zuoyong = liuyue_details['第二步_流月与各方作用分析']['天干作用详情']
                
                # 查找天干五合的情况
                for zuoyong in tiangan_zuoyong:
                    if '天干五合' in zuoyong['关系']:
                        print(f"  流月发现天干五合: {zuoyong['对象']} - {zuoyong['关系']}")
                        print(f"    计算: {zuoyong['计算']}")
                        print(f"    得分: {zuoyong['得分']}")
                        
                        # 检查是否有错误的格式
                        if '+ 1 + 1' in str(zuoyong['计算']):
                            print(f"    ❌ 发现错误格式: {zuoyong['计算']}")
                        else:
                            print(f"    ✅ 格式正确")
                        print()

if __name__ == "__main__":
    test_all_wuhe()
