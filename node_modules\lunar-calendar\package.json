{"author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://www.2fz1.com/)", "name": "lunar-calendar", "description": "农历（阴历）万年历，是一款支持Node.js和浏览器端使用的全功能农历和公历日历类库。支持农历与公历之间相互转换，含有二十四节气，天干地支纪年纪月纪日，生肖属相，公历节假日及农历传统节假日信息等功能。自带2013-2014节假日安排数据，并可自行配置。带有黄历数据，可自行选择配置。支持1891-2100年。", "keywords": ["chinese lunar", "lunar", "农历", "阴历", "万年历", "LunarCalendar", "calendar"], "version": "0.1.4", "repository": {"type": "git", "url": "https://github.com/zzyss86/LunarCalendar.git"}, "main": "./lib/LunarCalendar.js", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"grunt": "0.4.2", "grunt-contrib-uglify": "0.3.2", "mocha": "*", "expect.js": "*"}, "scripts": {"test": "make test"}}