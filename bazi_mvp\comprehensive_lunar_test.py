#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的农历转换库对比测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime

def test_zhdate(year, month, day):
    """测试zhdate库"""
    try:
        from zhdate import ZhDate
        solar_date = datetime(year, month, day)
        lunar_date = ZhDate.from_datetime(solar_date)
        return {
            'success': True,
            'lunar_year': lunar_date.year,
            'lunar_month': lunar_date.month,
            'lunar_day': lunar_date.day,
            'lunar_str': str(lunar_date),
            'library': 'zhdate'
        }
    except Exception as e:
        return {'success': False, 'error': str(e), 'library': 'zhdate'}

def test_our_converter(year, month, day):
    """测试我们的转换器"""
    try:
        from utils.calendar_converter import CalendarConverter
        converter = CalendarConverter()
        result = converter.solar_to_lunar(year, month, day)
        if result:
            return {
                'success': True,
                'lunar_year': result['lunar_year'],
                'lunar_month': result['lunar_month'],
                'lunar_day': result['lunar_day'],
                'lunar_str': result['lunar_date_full'],
                'accuracy': result['conversion_accuracy'],
                'library': '自研转换器'
            }
        else:
            return {'success': False, 'error': '返回None', 'library': '自研转换器'}
    except Exception as e:
        return {'success': False, 'error': str(e), 'library': '自研转换器'}

def test_single_date(year, month, day, description=""):
    """测试单个日期"""
    print(f"\n{'='*60}")
    print(f"测试日期: {year}年{month}月{day}日 {description}")
    print(f"{'='*60}")
    
    # 测试各个库
    results = []
    
    # 测试zhdate
    zhdate_result = test_zhdate(year, month, day)
    results.append(zhdate_result)
    if zhdate_result['success']:
        print(f"✅ zhdate: {zhdate_result['lunar_str']}")
    else:
        print(f"❌ zhdate: {zhdate_result['error']}")
    
    # 测试我们的转换器
    our_result = test_our_converter(year, month, day)
    results.append(our_result)
    if our_result['success']:
        print(f"✅ 自研转换器: {our_result['lunar_str']} (精度: {our_result.get('accuracy', '未知')})")
    else:
        print(f"❌ 自研转换器: {our_result['error']}")
    
    # 分析结果
    valid_results = [r for r in results if r['success']]
    
    if len(valid_results) >= 2:
        print(f"\n📊 结果对比:")
        years = [r['lunar_year'] for r in valid_results]
        months = [r['lunar_month'] for r in valid_results]
        days = [r['lunar_day'] for r in valid_results]
        
        print(f"农历年份: {years} - {'✅ 一致' if len(set(years)) == 1 else '❌ 不一致'}")
        print(f"农历月份: {months} - {'✅ 一致' if len(set(months)) == 1 else '❌ 不一致'}")
        print(f"农历日期: {days} - {'✅ 一致' if len(set(days)) == 1 else '❌ 不一致'}")
        
        # 详细对比
        for result in valid_results:
            print(f"  {result['library']}: {result['lunar_year']}年{result['lunar_month']}月{result['lunar_day']}日")
    else:
        print("⚠️  有效结果不足，无法进行对比")

def main():
    """主测试函数"""
    print("🌙 农历转换库全面对比测试")
    print("=" * 60)
    
    # 测试日期列表
    test_dates = [
        (1990, 9, 2, "您提到的测试日期"),
        (1990, 1, 1, "1990年元旦"),
        (2000, 2, 5, "2000年春节"),
        (2023, 2, 14, "2023年情人节"),
        (2024, 1, 1, "2024年元旦"),
        (2024, 2, 10, "2024年春节"),
        (1995, 8, 20, "1995年8月20日"),
        (2025, 1, 29, "2025年1月29日"),
    ]
    
    for year, month, day, desc in test_dates:
        test_single_date(year, month, day, desc)
    
    print(f"\n{'='*60}")
    print("🎯 测试总结")
    print(f"{'='*60}")
    print("1. zhdate: 轻量级农历转换库，支持1900-2100年")
    print("2. 自研转换器: 项目自开发的转换器，有精确对照表")
    print("\n📈 对比结果:")
    print("- 如果两个库结果一致，说明转换准确")
    print("- 如果有差异，需要进一步验证哪个更准确")
    print("- 建议使用zhdate作为主要转换库，自研转换器作为备用")

if __name__ == "__main__":
    main()

