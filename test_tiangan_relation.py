"""
测试天干关系计算
"""

from comprehensive_result import ComprehensiveAnalyzer

def test_tiangan_relation():
    """测试天干关系计算的返回值"""
    analyzer = ComprehensiveAnalyzer()
    
    # 测试普通生克关系
    print("=== 测试普通生克关系 ===")
    
    # 比和关系：甲 vs 甲
    result = analyzer._calculate_tiangan_relation('甲', '甲')
    print(f"甲 vs 甲: {result} (长度: {len(result)})")
    
    # 相生关系：甲 vs 丙
    result = analyzer._calculate_tiangan_relation('甲', '丙')
    print(f"甲 vs 丙: {result} (长度: {len(result)})")
    
    # 相克关系：甲 vs 戊
    result = analyzer._calculate_tiangan_relation('甲', '戊')
    print(f"甲 vs 戊: {result} (长度: {len(result)})")
    
    # 无直接关系：甲 vs 辛
    result = analyzer._calculate_tiangan_relation('甲', '辛')
    print(f"甲 vs 辛: {result} (长度: {len(result)})")
    
    print("\n=== 测试天干五合关系 ===")
    
    # 天干五合：甲 vs 己
    result = analyzer._calculate_tiangan_relation('甲', '己')
    print(f"甲 vs 己: {result} (长度: {len(result)})")
    
    # 天干五合：乙 vs 庚
    result = analyzer._calculate_tiangan_relation('乙', '庚')
    print(f"乙 vs 庚: {result} (长度: {len(result)})")

if __name__ == "__main__":
    test_tiangan_relation()
