"""
测试简洁版输出中的五行力量分布
"""

from comprehensive_result import ComprehensiveAnalyzer

def test_simple_output():
    """测试简洁版输出"""
    analyzer = ComprehensiveAnalyzer()
    
    # 测试案例
    result = analyzer.analyze_complete_bazi(
        birth_year=1990,
        birth_month=5,
        birth_day=15,
        birth_hour=15,
        birth_minute=0,
        city_name="广州",
        name="测试用户",
        gender="男"
    )
    
    print("=== 检查五行力量数据结构 ===")
    wuxing_info = result['五行分析']
    
    print("五行力量数据：")
    if '五行力量' in wuxing_info:
        for wuxing, info in wuxing_info['五行力量'].items():
            print(f"  {wuxing}: {info}")
    else:
        print("  五行力量数据不存在！")
    
    print("\n=== 生成简洁版输出 ===")
    simple_output = analyzer.format_simple_output(result)
    
    # 保存简洁版输出
    with open("简洁版输出测试.txt", "w", encoding="utf-8") as f:
        f.write(simple_output)
    
    print("简洁版输出已保存到: 简洁版输出测试.txt")
    
    # 检查五行力量分布部分
    if "五行力量分布：" in simple_output:
        print("✅ 五行力量分布部分存在")
        # 提取五行力量分布部分
        lines = simple_output.split('\n')
        in_wuxing_section = False
        for line in lines:
            if "五行力量分布：" in line:
                in_wuxing_section = True
                continue
            if in_wuxing_section and line.strip():
                if line.startswith('【'):
                    break
                print(f"  {line}")
    else:
        print("❌ 五行力量分布部分不存在")

if __name__ == "__main__":
    test_simple_output()
