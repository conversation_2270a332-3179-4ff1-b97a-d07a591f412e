"""
测试交互模式的详细过程显示
"""

from comprehensive_result import ComprehensiveAnalyzer

def test_interactive_details():
    """测试交互模式的详细过程"""
    analyzer = ComprehensiveAnalyzer()
    
    # 模拟交互模式的调用
    result = analyzer.analyze_complete_bazi(
        birth_year=1991,
        birth_month=5,
        birth_day=25,
        birth_hour=15,
        birth_minute=0,
        city_name="广州",
        name="徐尧",
        gender="男"
    )
    
    # 检查结果结构
    print("=== 数据结构检查 ===")
    print("基本信息键:", list(result["基本信息"].keys()))
    print("五行分析键:", list(result["五行分析"].keys()))
    print("十神分析键:", list(result["十神分析"].keys()))
    print("大运流年键:", list(result["大运流年"].keys()))
    
    # 检查是否有计算详情
    print("\n=== 计算详情检查 ===")
    print("五行计算详情存在:", "计算详情" in result["五行分析"])
    print("十神计算详情存在:", "计算详情" in result["十神分析"])
    print("大运计算详情存在:", "计算详情" in result["大运流年"])
    print("流年计算详情存在:", "流年计算详情" in result["大运流年"])
    
    if "计算详情" in result["五行分析"]:
        print("五行计算详情键:", list(result["五行分析"]["计算详情"].keys()))
    
    # 格式化输出
    print("\n=== 格式化输出测试 ===")
    formatted_result = analyzer.format_comprehensive_output(result)
    
    # 检查是否包含详细过程
    has_details = "详细过程" in formatted_result
    print(f"输出包含详细过程: {has_details}")
    
    if has_details:
        print("✅ 交互模式应该能正确显示详细过程")
    else:
        print("❌ 交互模式缺少详细过程显示")
        
    # 保存完整输出用于检查
    with open("interactive_test_output.txt", "w", encoding="utf-8") as f:
        f.write(formatted_result)
    print("完整输出已保存到 interactive_test_output.txt")

if __name__ == "__main__":
    test_interactive_details()
