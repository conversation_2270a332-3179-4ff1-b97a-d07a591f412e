"""
综合结果模块
汇总所有模块结果，提供统一的输出接口
"""

import json
from calendar_converter import CalendarConverter
from wuxing_calculator import WuxingCalculator
from shishen_calculator import ShishenCalculator
from dayun_calculator import DayunCalculator


class ComprehensiveAnalyzer:
    def __init__(self, config_path="config.json"):
        """初始化综合分析器"""
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        # 初始化各个模块
        self.calendar_converter = CalendarConverter(config_path)
        self.wuxing_calculator = WuxingCalculator(config_path)
        self.shishen_calculator = ShishenCalculator(config_path)
        self.dayun_calculator = DayunCalculator(config_path)
    
    def analyze_complete_bazi(self, birth_year, birth_month, birth_day,
                             birth_hour, birth_minute, city_name, name, gender='男'):
        """完整的八字分析"""

        # 1. 阳历阴历换算
        print("正在进行阳历阴历换算...")
        calendar_result = self.calendar_converter.process_birth_info(
            birth_year, birth_month, birth_day, birth_hour, birth_minute, city_name, name
        )

        if calendar_result is None:
            return {"错误": "阳历阴历换算失败"}

        # 2. 五行计算
        print("正在计算五行力量...")
        wuxing_scores, season = self.wuxing_calculator.calculate_wuxing_scores(
            calendar_result['四柱'], calendar_result['阴历信息']
        )

        # 获取五行计算的详细过程
        wuxing_details = self._get_wuxing_calculation_details(calendar_result['四柱'], season)

        # 3. 十神计算
        print("正在分析十神...")
        shishen_weights = self.shishen_calculator.calculate_shishen_weights(calendar_result['四柱'])
        body_analysis = self.shishen_calculator.analyze_body_strength(shishen_weights)
        xiyong_info = self.shishen_calculator.determine_xiyongshen(body_analysis[0], shishen_weights)
        spouse_info = self.shishen_calculator.analyze_spouse_info(calendar_result['四柱'], gender)

        # 获取十神计算的详细过程
        shishen_details = self._get_shishen_calculation_details(calendar_result['四柱'], shishen_weights)

        # 4. 流年大运计算
        print("正在分析大运流年...")
        qiyun_info = self.dayun_calculator.calculate_qiyun_time(calendar_result, gender)
        dayun_list = self.dayun_calculator.generate_dayun_sequence(
            calendar_result['四柱']['月柱'], qiyun_info['起运方向']
        )
        dayun_ages = self.dayun_calculator.calculate_dayun_ages(qiyun_info)

        # 获取大运计算的详细过程
        dayun_details = self._get_dayun_calculation_details(calendar_result, gender, qiyun_info)

        # 流年流月流日分析
        target_date = self.config['流年流月流日分析日期']
        liunian_result = self.dayun_calculator.analyze_liunian(
            calendar_result, dayun_list[0], xiyong_info, target_date
        )
        liuyue_result = self.dayun_calculator.analyze_liuyue(liunian_result, target_date)
        liuri_result = self.dayun_calculator.analyze_liuri(
            liunian_result, liuyue_result, target_date
        )

        # 5. 合婚计算（预留接口）
        marriage_result = self.analyze_marriage_compatibility()

        # 汇总结果
        comprehensive_result = {
            "基本信息": calendar_result,
            "五行分析": {
                "五行力量": wuxing_scores,
                "当前季节": season,
                "计算详情": wuxing_details
            },
            "十神分析": {
                "十神权重": shishen_weights,
                "身强身弱": body_analysis,
                "喜用神忌神": xiyong_info,
                "夫妻宫配偶星": spouse_info,
                "计算详情": shishen_details
            },
            "大运流年": {
                "起运信息": qiyun_info,
                "大运序列": list(zip(dayun_list, dayun_ages)),
                "流年分析": liunian_result,
                "流月分析": liuyue_result,
                "流日分析": liuri_result,
                "计算详情": dayun_details
            },
            "合婚分析": marriage_result
        }

        return comprehensive_result

    def _get_wuxing_calculation_details(self, sizhu, season):
        """获取五行计算的详细过程"""
        details = {
            "天干五行统计": {},
            "地支本气统计": {},
            "地支藏干详情": {},
            "季节旺度影响": {},
            "生扶克制计算": {},
            "最终力量计算": {}
        }

        # 天干五行统计
        tiangan_wuxing = {}
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]),
                        ('日干', sizhu['日柱'][0]), ('时干', sizhu['时柱'][0])]:
            wuxing = self.wuxing_calculator.tiangan_info[gan]['五行']
            if wuxing not in tiangan_wuxing:
                tiangan_wuxing[wuxing] = []
            tiangan_wuxing[wuxing].append(f"{pos}({gan})")
        details["天干五行统计"] = tiangan_wuxing

        # 地支本气统计
        dizhi_wuxing = {}
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            wuxing = self.wuxing_calculator.dizhi_info[zhi]['五行']
            if wuxing not in dizhi_wuxing:
                dizhi_wuxing[wuxing] = []
            dizhi_wuxing[wuxing].append(f"{pos}({zhi})")
        details["地支本气统计"] = dizhi_wuxing

        # 地支藏干详情
        canggan_details = {}
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            canggan_dict = self.wuxing_calculator.dizhi_info[zhi]['藏干']
            canggan_info = []
            for gan, weight in canggan_dict.items():
                wuxing = self.wuxing_calculator.tiangan_info[gan]['五行']
                canggan_info.append(f"{gan}({wuxing}) 权重{weight}")
            canggan_details[f"{pos}({zhi})"] = canggan_info
        details["地支藏干详情"] = canggan_details

        # 季节旺度影响
        season_effects = self.wuxing_calculator.season_wangdu[season]
        details["季节旺度影响"] = {
            "当前季节": season,
            "旺": f"{season_effects['旺']} (得令权重: {self.config['五行计算参数']['得令权重']})",
            "死": f"{season_effects['死']} (失令权重: {self.config['五行计算参数']['失令权重']})",
            "其他": f"{season_effects['相']}, {season_effects['休']}, {season_effects['囚']} (平和)"
        }

        return details

    def _get_shishen_calculation_details(self, sizhu, shishen_weights):
        """获取十神计算的详细过程"""
        day_gan = sizhu['日柱'][0]
        details = {
            "日主信息": {},
            "天干十神分析": {},
            "地支藏干十神分析": {},
            "十神权重汇总": {}
        }

        # 日主信息
        day_info = self.shishen_calculator.tiangan_info[day_gan]
        details["日主信息"] = {
            "日干": day_gan,
            "五行": day_info['五行'],
            "阴阳": day_info['阴阳']
        }

        # 天干十神分析
        tiangan_shishen = {}
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]), ('时干', sizhu['时柱'][0])]:
            if gan != day_gan:
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                tiangan_shishen[f"{pos}({gan})"] = f"{shishen} (权重: 1.0)"
        details["天干十神分析"] = tiangan_shishen

        # 地支藏干十神分析
        dizhi_shishen = {}
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            canggan_dict = self.shishen_calculator.dizhi_canggan[zhi]
            canggan_analysis = []
            for canggan, weight in canggan_dict.items():
                if canggan != day_gan:
                    shishen = self.shishen_calculator.determine_shishen(day_gan, canggan)
                    canggan_analysis.append(f"{canggan}→{shishen} (权重: {weight})")
            if canggan_analysis:
                dizhi_shishen[f"{pos}({zhi})"] = canggan_analysis
        details["地支藏干十神分析"] = dizhi_shishen

        # 十神权重汇总
        details["十神权重汇总"] = {k: f"{v:.2f}" for k, v in shishen_weights.items()}

        return details

    def _get_dayun_calculation_details(self, birth_info, gender, qiyun_info):
        """获取大运计算的详细过程"""
        year_gan = birth_info['四柱']['年柱'][0]

        details = {
            "起运规则判断": {},
            "节气距离计算": {},
            "起运时间换算": {},
            "大运排列规则": {}
        }

        # 起运规则判断
        yang_gan = ['甲', '丙', '戊', '庚', '壬']
        is_yang_gan = year_gan in yang_gan
        gan_type = "阳干" if is_yang_gan else "阴干"

        if (is_yang_gan and gender == '男') or (not is_yang_gan and gender == '女'):
            rule = "阳男阴女顺排"
            direction = "顺排"
        else:
            rule = "阴男阳女逆排"
            direction = "逆排"

        details["起运规则判断"] = {
            "年干": f"{year_gan} ({gan_type})",
            "性别": gender,
            "适用规则": rule,
            "排列方向": direction
        }

        # 节气距离计算
        details["节气距离计算"] = {
            "距离节气天数": f"{qiyun_info['距离节气天数']}天",
            "计算方法": "基于月份节气大致日期的简化计算"
        }

        # 起运时间换算
        days = qiyun_info['距离节气天数']
        years = int(days // 3)
        remaining_days = days % 3
        months = int(remaining_days * 4)

        details["起运时间换算"] = {
            "换算公式": "3天 = 1年, 1天 = 4个月",
            "计算过程": f"{days}天 ÷ 3 = {years}年 余 {remaining_days:.2f}天",
            "月份计算": f"{remaining_days:.2f}天 × 4 = {months}个月",
            "最终结果": f"{years}岁{months}个月"
        }

        # 大运排列规则
        month_pillar = birth_info['四柱']['月柱']
        details["大运排列规则"] = {
            "起始月柱": month_pillar,
            "排列方向": direction,
            "每步大运": "10年"
        }

        return details

    def analyze_marriage_compatibility(self):
        """合婚分析（预留接口）"""
        return {
            "状态": "功能暂未实现",
            "说明": "合婚计算模块预留接口，后续开发"
        }
    
    def format_comprehensive_output(self, result):
        """格式化综合输出结果"""
        if "错误" in result:
            return f"分析失败：{result['错误']}"
        
        basic_info = result["基本信息"]
        wuxing_info = result["五行分析"]
        shishen_info = result["十神分析"]
        dayun_info = result["大运流年"]
        
        output = f"""
{'='*50}
八字占卜系统 - 综合分析报告
{'='*50}

【基本信息】
姓名：{basic_info['姓名']}
性别：男  # 可根据需要调整
出生地：{basic_info['出生地']} (经度: {basic_info['经度']}°)

真太阳时计算详细过程：
1. 北京时间：{basic_info['北京时间']}
2. 经度时差：{basic_info['经度时差']} (公式: (120° - 当地经度) × 4分钟)
3. 均时差：{basic_info['均时差']} (地球椭圆轨道修正)
4. 真太阳时：{basic_info['真太阳时']} (北京时间 + 经度时差 + 均时差)

【四柱八字】
年柱：{basic_info['四柱']['年柱']}
月柱：{basic_info['四柱']['月柱']}
日柱：{basic_info['四柱']['日柱']}
时柱：{basic_info['四柱']['时柱']}
节气：{basic_info['阴历信息']['节气']}

【五行分析】
当前季节：{wuxing_info['当前季节']}

五行计算详细过程：
1. 天干五行统计：
"""
        for wuxing, positions in wuxing_info['计算详情']['天干五行统计'].items():
            output += f"   {wuxing}：{', '.join(positions)}\n"

        output += f"""
2. 地支本气统计：
"""
        for wuxing, positions in wuxing_info['计算详情']['地支本气统计'].items():
            output += f"   {wuxing}：{', '.join(positions)}\n"

        output += f"""
3. 地支藏干详情：
"""
        for position, canggan_list in wuxing_info['计算详情']['地支藏干详情'].items():
            output += f"   {position}：{', '.join(canggan_list)}\n"

        output += f"""
4. 季节旺度影响：
   当前季节：{wuxing_info['计算详情']['季节旺度影响']['当前季节']}
   得令（旺）：{wuxing_info['计算详情']['季节旺度影响']['旺']}
   失令（死）：{wuxing_info['计算详情']['季节旺度影响']['死']}
   平和：{wuxing_info['计算详情']['季节旺度影响']['其他']}

5. 最终五行力量分布：
"""

        for wuxing, info in wuxing_info['五行力量'].items():
            output += f"   {wuxing}：{info['力量值']} ({info['状态']}, {info['季节状态']})\n"
        
        body_strength, support_power, drain_power = shishen_info['身强身弱']
        xiyong_list, ji_list = shishen_info['喜用神忌神']
        
        output += f"""
【十神分析】
日主信息：{shishen_info['计算详情']['日主信息']['日干']} ({shishen_info['计算详情']['日主信息']['五行']}{shishen_info['计算详情']['日主信息']['阴阳']})

十神计算详细过程：
1. 天干十神分析：
"""
        for position, analysis in shishen_info['计算详情']['天干十神分析'].items():
            output += f"   {position}：{analysis}\n"

        output += f"""
2. 地支藏干十神分析：
"""
        for position, canggan_list in shishen_info['计算详情']['地支藏干十神分析'].items():
            output += f"   {position}：\n"
            for canggan_analysis in canggan_list:
                output += f"     {canggan_analysis}\n"

        output += f"""
3. 十神权重汇总：
"""
        for shishen, weight in shishen_info['十神权重'].items():
            output += f"   {shishen}：{weight:.2f}\n"

        output += f"""
4. 身强身弱判断：
   生扶日主力量：{support_power:.2f} (正印+偏印+比肩+劫财)
   克泄耗日主力量：{drain_power:.2f} (正官+七杀+食神+伤官+正财+偏财)
   判断结果：{body_strength}
"""
        
        output += f"""
喜用神："""
        for shishen, weight in xiyong_list[:4]:
            output += f" {shishen}({weight:.2f})"
        
        output += f"""
忌神："""
        for shishen, weight in ji_list[:4]:
            output += f" {shishen}({weight:.2f})"
        
        output += f"""

夫妻宫：{shishen_info['夫妻宫配偶星']['夫妻宫']}
配偶星：{shishen_info['夫妻宫配偶星']['配偶星']}

【大运流年分析】

大运起运计算详细过程：
1. 起运规则判断：
   年干：{dayun_info['计算详情']['起运规则判断']['年干']}
   性别：{dayun_info['计算详情']['起运规则判断']['性别']}
   适用规则：{dayun_info['计算详情']['起运规则判断']['适用规则']}
   排列方向：{dayun_info['计算详情']['起运规则判断']['排列方向']}

2. 节气距离计算：
   {dayun_info['计算详情']['节气距离计算']['距离节气天数']}
   计算方法：{dayun_info['计算详情']['节气距离计算']['计算方法']}

3. 起运时间换算：
   换算公式：{dayun_info['计算详情']['起运时间换算']['换算公式']}
   计算过程：{dayun_info['计算详情']['起运时间换算']['计算过程']}
   月份计算：{dayun_info['计算详情']['起运时间换算']['月份计算']}
   最终结果：{dayun_info['计算详情']['起运时间换算']['最终结果']}

4. 大运排列规则：
   起始月柱：{dayun_info['计算详情']['大运排列规则']['起始月柱']}
   排列方向：{dayun_info['计算详情']['大运排列规则']['排列方向']}
   每步大运：{dayun_info['计算详情']['大运排列规则']['每步大运']}

5. 大运序列：
"""

        for dayun, (start_age, end_age) in dayun_info['大运序列']:
            output += f"   {start_age}-{end_age}岁：{dayun}运\n"
        
        liunian = dayun_info['流年分析']
        liuyue = dayun_info['流月分析']
        liuri = dayun_info['流日分析']
        
        output += f"""
流年分析：{liunian['流年']} ({liunian['流年干支']})
  吉凶等级：{liunian['吉凶等级']} (得分: {liunian['总得分']})
  运势解读：{liunian['运势解读']}

流月分析：{liuyue['流月']} ({liuyue['流月干支']})
  得分：{liuyue['总得分']}

流日分析：{liuri['流日']} ({liuri['流日干支']})
  得分：{liuri['总得分']}

【合婚分析】
{result['合婚分析']['状态']}
{result['合婚分析']['说明']}

{'='*50}
分析完成
{'='*50}
"""
        
        return output
    
    def get_api_result(self, result):
        """获取API格式的结果（为前端接口预留）"""
        if "错误" in result:
            return {"success": False, "error": result["错误"]}
        
        return {
            "success": True,
            "data": {
                "basic_info": result["基本信息"],
                "wuxing_analysis": result["五行分析"],
                "shishen_analysis": result["十神分析"],
                "dayun_analysis": result["大运流年"],
                "marriage_analysis": result["合婚分析"]
            }
        }


# 测试函数
def test_comprehensive_analyzer():
    """测试综合分析器"""
    analyzer = ComprehensiveAnalyzer()
    
    # 进行完整分析
    result = analyzer.analyze_complete_bazi(
        birth_year=1990,
        birth_month=5,
        birth_day=15,
        birth_hour=14,
        birth_minute=30,
        city_name="广州",
        name="测试用户",
        gender="男"
    )
    
    # 输出格式化结果
    formatted_output = analyzer.format_comprehensive_output(result)
    print(formatted_output)
    
    return result


if __name__ == "__main__":
    test_comprehensive_analyzer()
