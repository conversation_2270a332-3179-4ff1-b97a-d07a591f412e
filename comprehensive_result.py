"""
综合结果模块
汇总所有模块结果，提供统一的输出接口
"""

import json
from calendar_converter import CalendarConverter
from wuxing_calculator import WuxingCalculator
from shishen_calculator import ShishenCalculator
from dayun_calculator import DayunCalculator


class ComprehensiveAnalyzer:
    def __init__(self, config_path="config.json"):
        """初始化综合分析器"""
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        # 初始化各个模块
        self.calendar_converter = CalendarConverter(config_path)
        self.wuxing_calculator = WuxingCalculator(config_path)
        self.shishen_calculator = ShishenCalculator(config_path)
        self.dayun_calculator = DayunCalculator(config_path)
    
    def analyze_complete_bazi(self, birth_year, birth_month, birth_day,
                             birth_hour, birth_minute, city_name, name, gender='男'):
        """完整的八字分析"""

        # 1. 阳历阴历换算
        print("正在进行阳历阴历换算...")
        calendar_result = self.calendar_converter.process_birth_info(
            birth_year, birth_month, birth_day, birth_hour, birth_minute, city_name, name
        )

        if calendar_result is None:
            return {"错误": "阳历阴历换算失败"}

        # 2. 五行计算
        print("正在计算五行力量...")
        wuxing_scores, season = self.wuxing_calculator.calculate_wuxing_scores(
            calendar_result['四柱'], calendar_result['阴历信息']
        )

        # 获取五行计算的详细过程
        wuxing_details = self._get_wuxing_calculation_details(calendar_result['四柱'], season)

        # 3. 十神计算
        print("正在分析十神...")
        shishen_weights = self.shishen_calculator.calculate_shishen_weights(calendar_result['四柱'])
        body_analysis = self.shishen_calculator.analyze_body_strength(shishen_weights)
        xiyong_info = self.shishen_calculator.determine_xiyongshen(body_analysis[0], shishen_weights)
        spouse_info = self.shishen_calculator.analyze_spouse_info(calendar_result['四柱'], gender)

        # 获取十神计算的详细过程
        shishen_details = self._get_shishen_calculation_details(calendar_result['四柱'], shishen_weights)

        # 4. 流年大运计算
        print("正在分析大运流年...")
        qiyun_info = self.dayun_calculator.calculate_qiyun_time(calendar_result, gender)
        dayun_list = self.dayun_calculator.generate_dayun_sequence(
            calendar_result['四柱']['月柱'], qiyun_info['起运方向']
        )
        dayun_ages = self.dayun_calculator.calculate_dayun_ages(qiyun_info)

        # 获取大运计算的详细过程
        dayun_details = self._get_dayun_calculation_details(calendar_result, gender, qiyun_info)

        # 流年流月流日分析
        target_date = self.config['流年流月流日分析日期']
        liunian_result = self.dayun_calculator.analyze_liunian(
            calendar_result, dayun_list[0], xiyong_info, target_date
        )
        liuyue_result = self.dayun_calculator.analyze_liuyue(liunian_result, target_date)
        liuri_result = self.dayun_calculator.analyze_liuri(
            liunian_result, liuyue_result, target_date
        )

        # 获取流年流月流日计算的详细过程
        liunian_details, updated_liunian_result = self._get_liunian_calculation_details(liunian_result, xiyong_info, target_date, calendar_result)

        # 使用更新后的流年结果进行流月流日计算
        liuyue_details, updated_liuyue_result = self._get_liuyue_calculation_details(liuyue_result, xiyong_info, target_date, calendar_result, dayun_list[0], updated_liunian_result)
        liuri_details, updated_liuri_result = self._get_liuri_calculation_details(liuri_result, xiyong_info, target_date, calendar_result, dayun_list[0], updated_liunian_result, updated_liuyue_result)

        # 5. 合婚计算（预留接口）
        marriage_result = self.analyze_marriage_compatibility()

        # 汇总结果
        comprehensive_result = {
            "基本信息": calendar_result,
            "五行分析": {
                "五行力量": wuxing_scores,
                "当前季节": season,
                "计算详情": wuxing_details
            },
            "十神分析": {
                "十神权重": shishen_weights,
                "身强身弱": body_analysis,
                "喜用神忌神": xiyong_info,
                "夫妻宫配偶星": spouse_info,
                "计算详情": shishen_details
            },
            "大运流年": {
                "起运信息": qiyun_info,
                "大运序列": list(zip(dayun_list, dayun_ages)),
                "流年分析": updated_liunian_result,
                "流月分析": updated_liuyue_result,
                "流日分析": updated_liuri_result,
                "计算详情": dayun_details,
                "流年计算详情": liunian_details,
                "流月计算详情": liuyue_details,
                "流日计算详情": liuri_details
            },
            "合婚分析": marriage_result
        }

        return comprehensive_result

    def _get_wuxing_calculation_details(self, sizhu, season):
        """获取五行计算的详细过程"""
        details = {
            "天干五行统计": {},
            "地支本气统计": {},
            "地支藏干详情": {},
            "季节旺度影响": {},
            "生扶克制计算": {},
            "最终力量计算": {}
        }

        # 天干五行统计
        tiangan_wuxing = {}
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]),
                        ('日干', sizhu['日柱'][0]), ('时干', sizhu['时柱'][0])]:
            wuxing = self.wuxing_calculator.tiangan_info[gan]['五行']
            if wuxing not in tiangan_wuxing:
                tiangan_wuxing[wuxing] = []
            tiangan_wuxing[wuxing].append(f"{pos}({gan})")
        details["天干五行统计"] = tiangan_wuxing

        # 地支本气统计
        dizhi_wuxing = {}
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            wuxing = self.wuxing_calculator.dizhi_info[zhi]['五行']
            if wuxing not in dizhi_wuxing:
                dizhi_wuxing[wuxing] = []
            dizhi_wuxing[wuxing].append(f"{pos}({zhi})")
        details["地支本气统计"] = dizhi_wuxing

        # 地支藏干详情
        canggan_details = {}
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            canggan_dict = self.wuxing_calculator.dizhi_info[zhi]['藏干']
            canggan_info = []
            for gan, weight in canggan_dict.items():
                wuxing = self.wuxing_calculator.tiangan_info[gan]['五行']
                canggan_info.append(f"{gan}({wuxing}) 权重{weight}")
            canggan_details[f"{pos}({zhi})"] = canggan_info
        details["地支藏干详情"] = canggan_details

        # 季节旺度影响
        season_effects = self.wuxing_calculator.season_wangdu[season]
        details["季节旺度影响"] = {
            "当前季节": season,
            "旺": f"{season_effects['旺']} (得令权重: {self.config['五行计算参数']['得令权重']})",
            "死": f"{season_effects['死']} (失令权重: {self.config['五行计算参数']['失令权重']})",
            "其他": f"{season_effects['相']}, {season_effects['休']}, {season_effects['囚']} (平和)"
        }

        # 详细的五行力量计算过程
        wuxing_detailed_calc = self._calculate_wuxing_detailed_steps(sizhu, season)
        details["五行力量详细计算"] = wuxing_detailed_calc

        return details

    def _get_shishen_calculation_details(self, sizhu, shishen_weights):
        """获取十神计算的详细过程"""
        day_gan = sizhu['日柱'][0]
        details = {
            "日主信息": {},
            "天干十神分析": {},
            "地支藏干十神分析": {},
            "十神权重汇总": {}
        }

        # 日主信息
        day_info = self.shishen_calculator.tiangan_info[day_gan]
        details["日主信息"] = {
            "日干": day_gan,
            "五行": day_info['五行'],
            "阴阳": day_info['阴阳']
        }

        # 天干十神分析
        tiangan_shishen = {}
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]), ('时干', sizhu['时柱'][0])]:
            if gan != day_gan:
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                tiangan_shishen[f"{pos}({gan})"] = f"{shishen} (权重: 1.0)"
        details["天干十神分析"] = tiangan_shishen

        # 地支藏干十神分析
        dizhi_shishen = {}
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            canggan_dict = self.shishen_calculator.dizhi_canggan[zhi]
            canggan_analysis = []
            for canggan, weight in canggan_dict.items():
                if canggan != day_gan:
                    shishen = self.shishen_calculator.determine_shishen(day_gan, canggan)
                    canggan_analysis.append(f"{canggan}→{shishen} (权重: {weight})")
            if canggan_analysis:
                dizhi_shishen[f"{pos}({zhi})"] = canggan_analysis
        details["地支藏干十神分析"] = dizhi_shishen

        # 十神权重汇总
        details["十神权重汇总"] = {k: f"{v:.2f}" for k, v in shishen_weights.items()}

        return details

    def _get_dayun_calculation_details(self, birth_info, gender, qiyun_info):
        """获取大运计算的详细过程"""
        year_gan = birth_info['四柱']['年柱'][0]

        details = {
            "起运规则判断": {},
            "节气距离计算": {},
            "起运时间换算": {},
            "大运排列规则": {}
        }

        # 起运规则判断
        yang_gan = ['甲', '丙', '戊', '庚', '壬']
        is_yang_gan = year_gan in yang_gan
        gan_type = "阳干" if is_yang_gan else "阴干"

        if (is_yang_gan and gender == '男') or (not is_yang_gan and gender == '女'):
            rule = "阳男阴女顺排"
            direction = "顺排"
        else:
            rule = "阴男阳女逆排"
            direction = "逆排"

        details["起运规则判断"] = {
            "年干": f"{year_gan} ({gan_type})",
            "性别": gender,
            "适用规则": rule,
            "排列方向": direction
        }

        # 节气距离计算
        details["节气距离计算"] = {
            "距离节气天数": f"{qiyun_info['距离节气天数']}天",
            "计算方法": "基于月份节气大致日期的简化计算"
        }

        # 起运时间换算
        days = qiyun_info['距离节气天数']
        years = int(days // 3)
        remaining_days = days % 3
        months = int(remaining_days * 4)

        details["起运时间换算"] = {
            "换算公式": "3天 = 1年, 1天 = 4个月",
            "计算过程": f"{days}天 ÷ 3 = {years}年 余 {remaining_days:.2f}天",
            "月份计算": f"{remaining_days:.2f}天 × 4 = {months}个月",
            "最终结果": f"{years}岁{months}个月"
        }

        # 大运排列规则
        month_pillar = birth_info['四柱']['月柱']
        details["大运排列规则"] = {
            "起始月柱": month_pillar,
            "排列方向": direction,
            "每步大运": "10年"
        }

        return details

    def _get_liunian_calculation_details(self, liunian_result, xiyong_info, target_date, birth_info):
        """获取流年流月流日计算的详细过程"""
        # 获取详细的计算步骤
        return self._calculate_liunian_detailed_steps(liunian_result, xiyong_info, target_date, birth_info)

    def _calculate_liunian_detailed_steps(self, liunian_result, xiyong_info, target_date, birth_info):
        """计算流年的详细步骤，展示每一步的公式和数值"""
        from lunar_python import Solar, Lunar

        details = {
            "第一步_流年干支获取": {},
            "第二步_原局四柱分析": {},
            "第三步_喜忌神权重计算": {},
            "第四步_流年与原局作用关系": {},
            "第五步_综合得分计算": {},
            "第六步_吉凶等级判定": {}
        }

        # 第一步：流年干支获取
        target_year = target_date['年']
        solar = Solar.fromYmd(target_year, 1, 1)
        lunar = solar.getLunar()
        liunian_ganzhi = lunar.getYearInGanZhi()
        liunian_gan, liunian_zhi = liunian_ganzhi[0], liunian_ganzhi[1]

        details["第一步_流年干支获取"] = {
            "计算目标": f"获取{target_year}年的干支",
            "使用工具": "lunar_python库",
            "计算过程": f"Solar.fromYmd({target_year}, 1, 1) -> getLunar() -> getYearInGanZhi()",
            "计算结果": f"{target_year}年 = {liunian_ganzhi} (天干:{liunian_gan}, 地支:{liunian_zhi})"
        }

        # 第二步：原局四柱分析
        sizhu = birth_info['四柱']
        tiangan_info = self.shishen_calculator.tiangan_info

        yuanju_analysis = {
            "年柱": f"{sizhu['年柱']} (天干:{sizhu['年柱'][0]}={tiangan_info[sizhu['年柱'][0]]['五行']}{tiangan_info[sizhu['年柱'][0]]['阴阳']}, 地支:{sizhu['年柱'][1]})",
            "月柱": f"{sizhu['月柱']} (天干:{sizhu['月柱'][0]}={tiangan_info[sizhu['月柱'][0]]['五行']}{tiangan_info[sizhu['月柱'][0]]['阴阳']}, 地支:{sizhu['月柱'][1]})",
            "日柱": f"{sizhu['日柱']} (天干:{sizhu['日柱'][0]}={tiangan_info[sizhu['日柱'][0]]['五行']}{tiangan_info[sizhu['日柱'][0]]['阴阳']}, 地支:{sizhu['日柱'][1]})",
            "时柱": f"{sizhu['时柱']} (天干:{sizhu['时柱'][0]}={tiangan_info[sizhu['时柱'][0]]['五行']}{tiangan_info[sizhu['时柱'][0]]['阴阳']}, 地支:{sizhu['时柱'][1]})"
        }

        details["第二步_原局四柱分析"] = {
            "日主": f"{sizhu['日柱'][0]} ({tiangan_info[sizhu['日柱'][0]]['五行']}{tiangan_info[sizhu['日柱'][0]]['阴阳']})",
            "原局四柱": yuanju_analysis
        }

        # 第三步：喜忌神权重计算
        xiyong_list, ji_list = xiyong_info

        xiyong_details = []
        for shishen, weight in xiyong_list[:4]:
            xiyong_details.append(f"{shishen}(权重:{weight:.2f})")

        ji_details = []
        for shishen, weight in ji_list[:4]:
            ji_details.append(f"{shishen}(权重:{weight:.2f})")

        details["第三步_喜忌神权重计算"] = {
            "计算依据": "基于身强身弱判断和十神权重分析",
            "喜用神": xiyong_details,
            "忌神": ji_details,
            "权重说明": "权重越高，对命主影响越大"
        }

        # 第四步：流年与原局作用关系（详细计算每个作用）
        day_gan = sizhu['日柱'][0]

        # 流年天干与原局天干的作用（包括日干）
        tiangan_zuoyong = []
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]), ('日干', sizhu['日柱'][0]), ('时干', sizhu['时柱'][0])]:
            # 计算天干关系（包含天干五合处理）
            relation_result = self._calculate_tiangan_relation(liunian_gan, gan, birth_info, None, liunian_ganzhi)

            if len(relation_result) == 3:  # 天干五合情况
                relation, huashen_or_coefficient, is_hehua_success = relation_result

                # 计算十神关系
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                gan_base_score, gan_xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)

                # 流年天干的十神关系（与日干比较）
                liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
                liunian_base_score, liunian_xiji_type = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)

                if huashen_or_coefficient:  # 合化成功
                    final_score, calculation_detail = self._calculate_wuhe_score(
                        liunian_gan, gan, liunian_base_score, gan_base_score,
                        huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
                    )

                    tiangan_zuoyong.append({
                        "原局": f"{pos}({gan})",
                        "五行关系": relation,
                        "十神": shishen,
                        "喜忌": gan_xiji_type,
                        "计算详情": calculation_detail,
                        "计算公式": f"详细计算: {calculation_detail['分数变化']} → {calculation_detail['总分']}",
                        "详细过程": calculation_detail,
                        "得分": final_score
                    })
                else:  # 合绊
                    final_score, calculation_detail = self._calculate_wuhe_score(
                        liunian_gan, gan, liunian_base_score, gan_base_score,
                        None, is_hehua_success, xiyong_list, ji_list, day_gan
                    )

                    tiangan_zuoyong.append({
                        "原局": f"{pos}({gan})",
                        "五行关系": relation,
                        "十神": shishen,
                        "喜忌": gan_xiji_type,
                        "计算详情": calculation_detail,
                        "计算公式": f"详细计算: {calculation_detail['合绊公式']}",
                        "详细过程": calculation_detail,
                        "得分": final_score
                    })
            else:  # 普通生克关系
                relation, coefficient = relation_result[0], relation_result[1]

                # 计算对象天干的十神关系和基准分
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)

                # 普通天干关系：对象基准分 × 关系系数
                final_score = base_score * coefficient

                liunian_wuxing = tiangan_info[liunian_gan]['五行']
                gan_wuxing = tiangan_info[gan]['五行']

                tiangan_zuoyong.append({
                    "原局": f"{pos}({gan})",
                    "五行关系": f"{liunian_gan}({liunian_wuxing}) vs {gan}({gan_wuxing}) = {relation}",
                    "十神": shishen,
                    "喜忌": xiji_type,
                    "计算公式": f"{base_score} × {coefficient} = {final_score:.2f}",
                    "得分": final_score
                })

        # 流年天干与大运干的作用
        # 这里需要获取当前大运信息，暂时使用示例
        dayun_gan = "壬"  # 从大运信息中获取
        dayun_zhi = "午"  # 从大运信息中获取

        # 计算流年天干与大运天干的关系
        dayun_relation_result = self._calculate_tiangan_relation(liunian_gan, dayun_gan, birth_info, (dayun_gan, dayun_zhi), liunian_ganzhi)

        if len(dayun_relation_result) == 3:  # 天干五合情况
            dayun_relation, huashen_or_coefficient, is_hehua_success = dayun_relation_result

            # 大运天干的十神关系
            dayun_shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_gan)
            dayun_base_score, dayun_xiji_type = self._get_xiji_score(dayun_shishen, xiyong_list, ji_list)

            # 流年天干的十神关系
            liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
            liunian_base_score, liunian_xiji_type = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)

            dayun_final_score, calculation_detail = self._calculate_wuhe_score(
                liunian_gan, dayun_gan, liunian_base_score, dayun_base_score,
                huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
            )

            tiangan_zuoyong.append({
                "原局": f"大运干({dayun_gan})",
                "五行关系": dayun_relation,
                "十神": dayun_shishen,
                "喜忌": dayun_xiji_type,
                "计算详情": calculation_detail,
                "计算公式": calculation_detail["总分"],
                "得分": dayun_final_score
            })
        else:  # 普通生克关系
            dayun_relation, dayun_coefficient = dayun_relation_result[0], dayun_relation_result[1]

            # 大运天干的十神关系和基准分
            dayun_shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_gan)
            dayun_base_score, dayun_xiji_type = self._get_xiji_score(dayun_shishen, xiyong_list, ji_list)

            # 普通天干关系：对象基准分 × 关系系数
            dayun_final_score = dayun_base_score * dayun_coefficient

            dayun_gan_wuxing = tiangan_info[dayun_gan]['五行']
            liunian_wuxing = tiangan_info[liunian_gan]['五行']

            tiangan_zuoyong.append({
                "原局": f"大运干({dayun_gan})",
                "五行关系": f"{liunian_gan}({liunian_wuxing}) vs {dayun_gan}({dayun_gan_wuxing}) = {dayun_relation}",
                "十神": dayun_shishen,
                "喜忌": dayun_xiji_type,
                "计算公式": f"{dayun_base_score} × {dayun_coefficient} = {dayun_final_score:.2f}",
                "得分": dayun_final_score
            })

        # 流年地支与原局地支的作用
        dizhi_zuoyong = []
        dizhi_info = self.wuxing_calculator.dizhi_info

        # 地支关系检查函数
        def check_dizhi_relation(zhi1, zhi2):
            # 地支六冲
            liuchong_pairs = [('子', '午'), ('丑', '未'), ('寅', '申'), ('卯', '酉'), ('辰', '戌'), ('巳', '亥')]
            if any((zhi1, zhi2) in [pair, pair[::-1]] for pair in liuchong_pairs):
                return "地支六冲", self.config['地支关系影响系数']['地支六冲']

            # 地支六合
            liuhe_pairs = [('子', '丑'), ('寅', '亥'), ('卯', '戌'), ('辰', '酉'), ('巳', '申'), ('午', '未')]
            if any((zhi1, zhi2) in [pair, pair[::-1]] for pair in liuhe_pairs):
                return "地支六合", self.config['地支关系影响系数']['地支六合']

            # 地支相害
            xianghai_pairs = [('子', '未'), ('丑', '午'), ('寅', '巳'), ('卯', '辰'), ('申', '亥'), ('酉', '戌')]
            if any((zhi1, zhi2) in [pair, pair[::-1]] for pair in xianghai_pairs):
                return "地支相害", self.config['地支关系影响系数']['地支相害']

            # 地支相刑
            if {zhi1, zhi2}.issubset({'寅', '巳', '申'}):
                return "地支相刑", self.config['地支关系影响系数']['地支相刑']
            elif {zhi1, zhi2}.issubset({'丑', '未', '戌'}):
                return "地支相刑", self.config['地支关系影响系数']['地支相刑']
            elif {zhi1, zhi2} == {'子', '卯'}:
                return "地支相刑", self.config['地支关系影响系数']['地支相刑']

            # 普通生克关系
            return "普通生克", 1.0

        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]), ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            relation_type, coefficient = check_dizhi_relation(liunian_zhi, zhi)

            # 地支的五行属性
            liunian_zhi_wuxing = dizhi_info[liunian_zhi]['五行']
            zhi_wuxing = dizhi_info[zhi]['五行']

            # 地支的十神关系（通过本气）
            zhi_bengqi = list(dizhi_info[zhi]['藏干'].keys())[0]  # 第一个藏干是本气
            zhi_shishen = self.shishen_calculator.determine_shishen(day_gan, zhi_bengqi)

            # 判断是喜是忌
            is_zhi_xiyong = any(zhi_shishen == item[0] for item in xiyong_list)
            is_zhi_ji = any(zhi_shishen == item[0] for item in ji_list)

            if is_zhi_xiyong:
                zhi_xiji_type = "喜用神"
                zhi_base_score = 1.0
            elif is_zhi_ji:
                zhi_xiji_type = "忌神"
                zhi_base_score = -1.0
            else:
                zhi_xiji_type = "中性"
                zhi_base_score = 0.0

            zhi_final_score = zhi_base_score * coefficient

            dizhi_zuoyong.append({
                "原局": f"{pos}({zhi})",
                "五行关系": f"{liunian_zhi}({liunian_zhi_wuxing}) vs {zhi}({zhi_wuxing}) = {relation_type}",
                "十神": zhi_shishen,
                "喜忌": zhi_xiji_type,
                "计算公式": f"{zhi_base_score} × {coefficient} = {zhi_final_score:.2f}",
                "得分": zhi_final_score
            })

        # 流年地支与大运地支的作用
        dayun_relation_type, dayun_zhi_coefficient = check_dizhi_relation(liunian_zhi, dayun_zhi)

        dayun_zhi_wuxing = dizhi_info[dayun_zhi]['五行']
        dayun_zhi_bengqi = list(dizhi_info[dayun_zhi]['藏干'].keys())[0]
        dayun_zhi_shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_zhi_bengqi)

        is_dayun_zhi_xiyong = any(dayun_zhi_shishen == item[0] for item in xiyong_list)
        is_dayun_zhi_ji = any(dayun_zhi_shishen == item[0] for item in ji_list)

        if is_dayun_zhi_xiyong:
            dayun_zhi_xiji_type = "喜用神"
            dayun_zhi_base_score = 1.0
        elif is_dayun_zhi_ji:
            dayun_zhi_xiji_type = "忌神"
            dayun_zhi_base_score = -1.0
        else:
            dayun_zhi_xiji_type = "中性"
            dayun_zhi_base_score = 0.0

        dayun_zhi_final_score = dayun_zhi_base_score * dayun_zhi_coefficient

        dizhi_zuoyong.append({
            "原局": f"大运支({dayun_zhi})",
            "五行关系": f"{liunian_zhi}({liunian_zhi_wuxing}) vs {dayun_zhi}({dayun_zhi_wuxing}) = {dayun_relation_type}",
            "十神": dayun_zhi_shishen,
            "喜忌": dayun_zhi_xiji_type,
            "计算公式": f"{dayun_zhi_base_score} × {dayun_zhi_coefficient} = {dayun_zhi_final_score:.2f}",
            "得分": dayun_zhi_final_score
        })

        details["第四步_流年与原局作用关系"] = {
            "流年干支": f"{liunian_gan}{liunian_zhi}",
            "天干作用详情": tiangan_zuoyong,
            "地支作用详情": dizhi_zuoyong
        }

        # 添加流年天干自身得分
        liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
        liunian_self_score, liunian_self_xiji = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)

        tiangan_zuoyong.append({
            "原局": f"流年干自身({liunian_gan})",
            "五行关系": "流年天干自身",
            "十神": liunian_shishen,
            "喜忌": liunian_self_xiji,
            "计算公式": f"{liunian_self_score} × 1.0 = {liunian_self_score:.2f}",
            "得分": liunian_self_score
        })

        # 添加流年地支自身得分
        liunian_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[liunian_zhi]['藏干'].keys())[0]
        liunian_zhi_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_zhi_bengqi)
        liunian_zhi_self_score, liunian_zhi_self_xiji = self._get_xiji_score(liunian_zhi_shishen, xiyong_list, ji_list)

        dizhi_zuoyong.append({
            "原局": f"流年支自身({liunian_zhi})",
            "五行关系": "流年地支自身",
            "十神": liunian_zhi_shishen,
            "喜忌": liunian_zhi_self_xiji,
            "计算公式": f"{liunian_zhi_self_score} × 1.0 = {liunian_zhi_self_score:.2f}",
            "得分": liunian_zhi_self_score
        })

        # 第五步：综合得分计算
        tiangan_total = sum(item["得分"] for item in tiangan_zuoyong)
        dizhi_total = sum(item["得分"] for item in dizhi_zuoyong)
        total_score = tiangan_total + dizhi_total

        details["第五步_综合得分计算"] = {
            "计算公式": "∑(天干作用得分) + ∑(地支作用得分)",
            "天干得分详细": " + ".join([f"({item['得分']:.2f})" for item in tiangan_zuoyong]),
            "天干小计": f"{tiangan_total:.2f}",
            "地支得分详细": " + ".join([f"({item['得分']:.2f})" for item in dizhi_zuoyong]),
            "地支小计": f"{dizhi_total:.2f}",
            "总得分": f"{total_score:.2f}"
        }

        # 第六步：吉凶等级判定
        config_levels = self.config['吉凶等级判定']

        if total_score >= config_levels['大吉']:
            level = '大吉'
            desc = '诸事顺遂'
        elif total_score >= config_levels['吉']:
            level = '吉'
            desc = '小有收获'
        elif total_score > config_levels['凶']:
            level = '平'
            desc = '平淡无奇'
        elif total_score >= config_levels['大凶']:
            level = '凶'
            desc = '阻力频生'
        else:
            level = '大凶'
            desc = '动荡不安'

        details["第六步_吉凶等级判定"] = {
            "判定标准": {
                "大吉": f"≥ {config_levels['大吉']}分",
                "吉": f"≥ {config_levels['吉']}分",
                "平": f"> {config_levels['凶']}分",
                "凶": f"≥ {config_levels['大凶']}分",
                "大凶": f"< {config_levels['大凶']}分"
            },
            "实际得分": total_score,
            "判定结果": level,
            "运势解读": desc
        }

        # 更新流年结果，使用我们详细计算的数据
        updated_liunian_result = {
            "流年": f"{target_year}年",
            "流年干支": liunian_ganzhi,
            "总得分": total_score,
            "吉凶等级": level,
            "运势解读": desc
        }

        return details, updated_liunian_result

    def _calculate_wuxing_detailed_steps(self, sizhu, season):
        """计算五行的详细步骤，展示每一步的公式和数值"""
        wuxing_steps = {
            "第一步_基础分数统计": {},
            "第二步_季节旺度调整": {},
            "第三步_藏干权重计算": {},
            "第四步_生扶克制计算": {},
            "第五步_最终力量汇总": {}
        }

        # 第一步：基础分数统计
        base_counts = {"木": 0, "火": 0, "土": 0, "金": 0, "水": 0}
        tiangan_detail = []

        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]),
                        ('日干', sizhu['日柱'][0]), ('时干', sizhu['时柱'][0])]:
            wuxing = self.wuxing_calculator.tiangan_info[gan]['五行']
            base_counts[wuxing] += 1
            tiangan_detail.append(f"{pos}({gan}) = {wuxing} +1")

        dizhi_detail = []
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            wuxing = self.wuxing_calculator.dizhi_info[zhi]['五行']
            base_counts[wuxing] += 1
            dizhi_detail.append(f"{pos}({zhi}) = {wuxing} +1")

        wuxing_steps["第一步_基础分数统计"] = {
            "天干统计": tiangan_detail,
            "地支本气统计": dizhi_detail,
            "基础分数": {k: f"{v}分" for k, v in base_counts.items()}
        }

        # 第二步：季节旺度调整
        season_effects = self.wuxing_calculator.season_wangdu[season]
        deling_weight = self.config['五行计算参数']['得令权重']
        shiling_weight = self.config['五行计算参数']['失令权重']

        season_adjusted = {}
        season_detail = []

        for wuxing, count in base_counts.items():
            if wuxing == season_effects['旺']:
                adjusted = count * deling_weight
                season_detail.append(f"{wuxing}: {count} × {deling_weight}(得令) = {adjusted}")
            elif wuxing == season_effects['死']:
                adjusted = count * shiling_weight
                season_detail.append(f"{wuxing}: {count} × {shiling_weight}(失令) = {adjusted}")
            else:
                adjusted = count * 1.0
                season_detail.append(f"{wuxing}: {count} × 1.0(平和) = {adjusted}")
            season_adjusted[wuxing] = adjusted

        wuxing_steps["第二步_季节旺度调整"] = {
            "调整公式": "基础分数 × 季节权重",
            "详细计算": season_detail,
            "调整后分数": {k: f"{v:.2f}分" for k, v in season_adjusted.items()}
        }

        # 第三步：藏干权重计算
        canggan_detail = []
        canggan_scores = {"木": 0, "火": 0, "土": 0, "金": 0, "水": 0}

        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            canggan_dict = self.wuxing_calculator.dizhi_info[zhi]['藏干']
            zhi_detail = []

            for gan, weight in canggan_dict.items():
                if gan != list(canggan_dict.keys())[0]:  # 跳过本气（已在第一步计算）
                    wuxing = self.wuxing_calculator.tiangan_info[gan]['五行']
                    score = weight
                    canggan_scores[wuxing] += score
                    zhi_detail.append(f"{gan}({wuxing}) × {weight} = {score}")

            if zhi_detail:
                canggan_detail.append(f"{pos}({zhi}): {', '.join(zhi_detail)}")

        wuxing_steps["第三步_藏干权重计算"] = {
            "计算说明": "地支藏干按权重计入对应五行",
            "详细计算": canggan_detail,
            "藏干得分": {k: f"{v:.2f}分" for k, v in canggan_scores.items()}
        }

        # 第四步：生扶克制计算
        # 合并季节调整分数和藏干分数
        combined_scores = {}
        for wuxing in ["木", "火", "土", "金", "水"]:
            combined_scores[wuxing] = season_adjusted[wuxing] + canggan_scores[wuxing]

        # 计算生扶克制
        shengfu_detail = []
        kezhi_detail = []
        final_scores = combined_scores.copy()

        shengfu_coeff = self.config['五行计算参数']['生扶系数']
        kezhi_coeff = self.config['五行计算参数']['克制系数']

        for wuxing in ["木", "火", "土", "金", "水"]:
            # 计算生我的力量
            sheng_wo = self.wuxing_calculator.wuxing_sheng_reverse.get(wuxing, "")
            if sheng_wo and sheng_wo in combined_scores:
                sheng_power = combined_scores[sheng_wo] * shengfu_coeff
                final_scores[wuxing] += sheng_power
                shengfu_detail.append(f"{wuxing} 得 {sheng_wo} 生扶: {combined_scores[sheng_wo]:.2f} × {shengfu_coeff} = +{sheng_power:.2f}")

            # 计算克我的力量
            ke_wo = self.wuxing_calculator.wuxing_ke_reverse.get(wuxing, "")
            if ke_wo and ke_wo in combined_scores:
                ke_power = combined_scores[ke_wo] * kezhi_coeff
                final_scores[wuxing] -= ke_power
                kezhi_detail.append(f"{wuxing} 被 {ke_wo} 克制: {combined_scores[ke_wo]:.2f} × {kezhi_coeff} = -{ke_power:.2f}")

        wuxing_steps["第四步_生扶克制计算"] = {
            "基础分数": {k: f"{v:.2f}分" for k, v in combined_scores.items()},
            "生扶计算": shengfu_detail,
            "克制计算": kezhi_detail,
            "生扶系数": shengfu_coeff,
            "克制系数": kezhi_coeff
        }

        # 第五步：最终力量汇总
        total_power = sum(final_scores.values())
        percentages = {k: (v/total_power)*100 if total_power > 0 else 0 for k, v in final_scores.items()}

        # 判断五行状态
        status_detail = []
        for wuxing, score in final_scores.items():
            if score >= 4.0:
                status = "过强"
            elif score >= 2.5:
                status = "中和偏强"
            elif score >= 1.5:
                status = "中和"
            elif score >= 0.5:
                status = "偏弱"
            else:
                status = "很弱"

            status_detail.append(f"{wuxing}: {score:.2f}分 ({percentages[wuxing]:.1f}%) = {status}")

        wuxing_steps["第五步_最终力量汇总"] = {
            "最终得分": {k: f"{v:.2f}分" for k, v in final_scores.items()},
            "总力量": f"{total_power:.2f}分",
            "力量占比": {k: f"{v:.1f}%" for k, v in percentages.items()},
            "状态判定": status_detail
        }

        return wuxing_steps

    def _calculate_wuxing_conversion_details(self, sizhu, detailed_scores, final_wuxing_power):
        """计算五行力量转换的详细过程"""
        conversion_details = {}

        # 获取基础数据
        basic_count = self.wuxing_calculator.calculate_basic_wuxing_count(sizhu)
        canggan_contrib = self.wuxing_calculator.calculate_canggan_contribution(sizhu)

        # 获取季节信息
        month_zhi = sizhu['月柱'][1]
        season = self.wuxing_calculator.get_season_from_month(month_zhi)
        season_wangdu = self.wuxing_calculator.season_wangdu[season]

        # 获取配置参数
        deling_weight = self.config['五行计算参数']['得令权重']
        shiling_weight = self.config['五行计算参数']['失令权重']
        shengfu_coeff = self.config['五行计算参数']['生扶系数']
        kezhi_coeff = self.config['五行计算参数']['克制系数']

        for wuxing in ['木', '火', '土', '金', '水']:
            details = {}

            # 1. 基础分数计算
            base_score = basic_count[wuxing] + canggan_contrib[wuxing]
            details["步骤1_基础分数"] = f"{basic_count[wuxing]} + {canggan_contrib[wuxing]:.2f} = {base_score:.2f}"

            # 2. 季节调整
            if wuxing == season_wangdu['旺']:
                score_after_season = base_score * deling_weight
                details["步骤2_季节调整"] = f"{base_score:.2f} × {deling_weight}(得令) = {score_after_season:.2f}"
            elif wuxing == season_wangdu['死']:
                score_after_season = base_score * shiling_weight
                details["步骤2_季节调整"] = f"{base_score:.2f} × {shiling_weight}(失令) = {score_after_season:.2f}"
            else:
                score_after_season = base_score
                details["步骤2_季节调整"] = f"{base_score:.2f} × 1.0(平和) = {score_after_season:.2f}"

            # 3. 生扶计算
            sheng_wuxing = None
            for wx, sheng_target in self.wuxing_calculator.wuxing_sheng.items():
                if sheng_target == wuxing:
                    sheng_wuxing = wx
                    break

            score_after_shengfu = score_after_season
            if sheng_wuxing:
                sheng_score = basic_count[sheng_wuxing] + canggan_contrib[sheng_wuxing]
                if sheng_score > 0:
                    shengfu_bonus = sheng_score * shengfu_coeff
                    score_after_shengfu = score_after_season + shengfu_bonus
                    details["步骤3_生扶加成"] = f"{score_after_season:.2f} + ({sheng_score:.2f} × {shengfu_coeff}) = {score_after_shengfu:.2f}"
                else:
                    details["步骤3_生扶加成"] = f"{score_after_season:.2f} + 0 = {score_after_shengfu:.2f}"
            else:
                details["步骤3_生扶加成"] = f"{score_after_season:.2f} + 0 = {score_after_shengfu:.2f}"

            # 4. 克制计算
            ke_wuxing = None
            for wx, ke_target in self.wuxing_calculator.wuxing_ke.items():
                if ke_target == wuxing:
                    ke_wuxing = wx
                    break

            score_after_kezhi = score_after_shengfu
            if ke_wuxing:
                ke_score = basic_count[ke_wuxing] + canggan_contrib[ke_wuxing]
                if ke_score > 0:
                    kezhi_penalty = ke_score * kezhi_coeff
                    score_after_kezhi = score_after_shengfu - kezhi_penalty
                    details["步骤4_克制减损"] = f"{score_after_shengfu:.2f} - ({ke_score:.2f} × {kezhi_coeff}) = {score_after_kezhi:.2f}"
                else:
                    details["步骤4_克制减损"] = f"{score_after_shengfu:.2f} - 0 = {score_after_kezhi:.2f}"
            else:
                details["步骤4_克制减损"] = f"{score_after_shengfu:.2f} - 0 = {score_after_kezhi:.2f}"

            # 5. 确保非负
            final_score = max(0, score_after_kezhi)
            if final_score != score_after_kezhi:
                details["步骤5_非负处理"] = f"max(0, {score_after_kezhi:.2f}) = {final_score:.2f}"
            else:
                details["步骤5_最终得分"] = f"{final_score:.2f}"

            conversion_details[wuxing] = details

        return conversion_details

    def _get_liuyue_calculation_details(self, liuyue_result, xiyong_info, target_date, birth_info, dayun, liunian_result):
        """获取流月计算的详细过程"""
        from lunar_python import Solar, Lunar

        details = {
            "第一步_流月干支获取": {},
            "第二步_流月与各方作用分析": {},
            "第三步_流月综合得分计算": {}
        }

        # 第一步：流月干支获取
        target_year = target_date['年']
        target_month = target_date['月']
        solar = Solar.fromYmd(target_year, target_month, 1)
        lunar = solar.getLunar()
        liuyue_ganzhi = lunar.getMonthInGanZhi()
        liuyue_gan, liuyue_zhi = liuyue_ganzhi[0], liuyue_ganzhi[1]

        details["第一步_流月干支获取"] = {
            "计算目标": f"获取{target_year}年{target_month}月的干支",
            "使用工具": "lunar_python库",
            "计算过程": f"Solar.fromYmd({target_year}, {target_month}, 1) -> getLunar() -> getMonthInGanZhi()",
            "计算结果": f"{target_year}年{target_month}月 = {liuyue_ganzhi} (天干:{liuyue_gan}, 地支:{liuyue_zhi})"
        }

        # 第二步：流月与各方作用分析
        sizhu = birth_info['四柱']
        day_gan = sizhu['日柱'][0]
        xiyong_list, ji_list = xiyong_info
        tiangan_info = self.shishen_calculator.tiangan_info

        # 流月天干与各方的作用
        liuyue_tiangan_zuoyong = []

        # 与原局天干作用
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]), ('日干', sizhu['日柱'][0]), ('时干', sizhu['时柱'][0])]:
            relation_result = self._calculate_tiangan_relation(liuyue_gan, gan, birth_info, dayun, (liunian_result['流年干支'][0], liunian_result['流年干支'][1]))

            if len(relation_result) == 3:  # 天干五合情况
                relation, huashen_or_coefficient, is_hehua_success = relation_result
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                gan_base_score, gan_xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)

                liuyue_shishen = self.shishen_calculator.determine_shishen(day_gan, liuyue_gan)
                liuyue_base_score, liuyue_xiji_type = self._get_xiji_score(liuyue_shishen, xiyong_list, ji_list)

                final_score, calculation_detail = self._calculate_wuhe_score(
                    liuyue_gan, gan, liuyue_base_score, gan_base_score,
                    huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
                )

                liuyue_tiangan_zuoyong.append({
                    "对象": f"原局{pos}({gan})",
                    "关系": relation,
                    "十神": shishen,
                    "喜忌": gan_xiji_type,
                    "计算": calculation_detail["总分"],
                    "得分": final_score
                })
            else:  # 普通生克关系
                relation, coefficient = relation_result[0], relation_result[1]
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
                final_score = base_score * coefficient

                liuyue_tiangan_zuoyong.append({
                    "对象": f"原局{pos}({gan})",
                    "关系": relation,
                    "十神": shishen,
                    "喜忌": xiji_type,
                    "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                    "得分": final_score
                })

        # 与大运天干作用
        dayun_gan = dayun[0]
        relation_result = self._calculate_tiangan_relation(liuyue_gan, dayun_gan, birth_info, dayun, (liunian_result['流年干支'][0], liunian_result['流年干支'][1]))

        if len(relation_result) == 3:  # 天干五合情况
            relation, huashen_or_coefficient, is_hehua_success = relation_result
            shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_gan)
            dayun_base_score, dayun_xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)

            liuyue_shishen = self.shishen_calculator.determine_shishen(day_gan, liuyue_gan)
            liuyue_base_score, liuyue_xiji_type = self._get_xiji_score(liuyue_shishen, xiyong_list, ji_list)

            final_score, calculation_detail = self._calculate_wuhe_score(
                liuyue_gan, dayun_gan, liuyue_base_score, dayun_base_score,
                huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
            )

            liuyue_tiangan_zuoyong.append({
                "对象": f"大运干({dayun_gan})",
                "关系": relation,
                "十神": shishen,
                "喜忌": dayun_xiji_type,
                "计算": calculation_detail["总分"],
                "得分": final_score
            })
        else:  # 普通生克关系
            relation, coefficient = relation_result[0], relation_result[1]
            shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_gan)
            base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuyue_tiangan_zuoyong.append({
                "对象": f"大运干({dayun_gan})",
                "关系": relation,
                "十神": shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 与流年天干作用
        liunian_gan = liunian_result['流年干支'][0]
        relation_result = self._calculate_tiangan_relation(liuyue_gan, liunian_gan, birth_info, dayun, (liunian_gan, liunian_result['流年干支'][1]))

        if len(relation_result) == 3:  # 天干五合情况
            relation, huashen_or_coefficient, is_hehua_success = relation_result
            liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
            liunian_base_score, liunian_xiji_type = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)

            liuyue_shishen = self.shishen_calculator.determine_shishen(day_gan, liuyue_gan)
            liuyue_base_score, liuyue_xiji_type = self._get_xiji_score(liuyue_shishen, xiyong_list, ji_list)

            final_score, calculation_detail = self._calculate_wuhe_score(
                liuyue_gan, liunian_gan, liuyue_base_score, liunian_base_score,
                huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
            )

            liuyue_tiangan_zuoyong.append({
                "对象": f"流年干({liunian_gan})",
                "关系": relation,
                "十神": liunian_shishen,
                "喜忌": liunian_xiji_type,
                "计算": calculation_detail["总分"],
                "得分": final_score
            })
        else:  # 普通生克关系
            relation, coefficient = relation_result[0], relation_result[1]
            liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
            base_score, xiji_type = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuyue_tiangan_zuoyong.append({
                "对象": f"流年干({liunian_gan})",
                "关系": relation,
                "十神": liunian_shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 流月地支与各方的作用
        liuyue_dizhi_zuoyong = []

        # 与原局地支作用
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]), ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            relation, coefficient = self._calculate_dizhi_relation(liuyue_zhi, zhi)
            # 地支通过本气确定十神
            zhi_bengqi = list(self.wuxing_calculator.dizhi_info[zhi]['藏干'].keys())[0]
            shishen = self.shishen_calculator.determine_shishen(day_gan, zhi_bengqi)
            base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuyue_dizhi_zuoyong.append({
                "对象": f"原局{pos}({zhi})",
                "关系": relation,
                "十神": shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 与大运地支作用
        dayun_zhi = dayun[1]
        relation, coefficient = self._calculate_dizhi_relation(liuyue_zhi, dayun_zhi)
        dayun_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[dayun_zhi]['藏干'].keys())[0]
        shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_zhi_bengqi)
        base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
        final_score = base_score * coefficient

        liuyue_dizhi_zuoyong.append({
            "对象": f"大运支({dayun_zhi})",
            "关系": relation,
            "十神": shishen,
            "喜忌": xiji_type,
            "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
            "得分": final_score
        })

        # 与流年地支作用
        liunian_zhi = liunian_result['流年干支'][1]
        relation, coefficient = self._calculate_dizhi_relation(liuyue_zhi, liunian_zhi)
        liunian_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[liunian_zhi]['藏干'].keys())[0]
        shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_zhi_bengqi)
        base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
        final_score = base_score * coefficient

        liuyue_dizhi_zuoyong.append({
            "对象": f"流年支({liunian_zhi})",
            "关系": relation,
            "十神": shishen,
            "喜忌": xiji_type,
            "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
            "得分": final_score
        })

        details["第二步_流月与各方作用分析"] = {
            "流月干支": f"{liuyue_gan}{liuyue_zhi}",
            "天干作用详情": liuyue_tiangan_zuoyong,
            "地支作用详情": liuyue_dizhi_zuoyong
        }

        # 第三步：流月综合得分计算
        tiangan_total = sum(item["得分"] for item in liuyue_tiangan_zuoyong)
        dizhi_total = sum(item["得分"] for item in liuyue_dizhi_zuoyong)
        liuyue_subtotal = tiangan_total + dizhi_total

        # 流月总得分需要叠加流年和大运的背景影响
        liunian_score = float(liunian_result['总得分'])
        dayun_score = -2.0  # 示例大运得分，实际应该从大运分析中获取
        liuyue_total = liuyue_subtotal + liunian_score + dayun_score

        details["第三步_流月综合得分计算"] = {
            "天干小计": f"{tiangan_total:.2f}",
            "地支小计": f"{dizhi_total:.2f}",
            "流月自身得分": f"{liuyue_subtotal:.2f}",
            "流年背景分": f"{liunian_score:.2f}",
            "大运背景分": f"{dayun_score:.2f}",
            "计算公式": "流月自身得分 + 流年背景分 + 大运背景分",
            "总得分": f"{liuyue_total:.2f}"
        }

        # 更新流月结果
        updated_liuyue_result = {
            "流月": f"{target_year}年{target_month}月",
            "流月干支": liuyue_ganzhi,
            "总得分": liuyue_total
        }

        return details, updated_liuyue_result

    def _get_liuri_calculation_details(self, liuri_result, xiyong_info, target_date, birth_info, dayun, liunian_result, liuyue_result):
        """获取流日计算的详细过程"""
        from lunar_python import Solar, Lunar

        details = {
            "第一步_流日干支获取": {},
            "第二步_流日与各方作用分析": {},
            "第三步_流日综合得分计算": {}
        }

        # 第一步：流日干支获取
        target_year = target_date['年']
        target_month = target_date['月']
        target_day = target_date['日']
        solar = Solar.fromYmd(target_year, target_month, target_day)
        lunar = solar.getLunar()
        liuri_ganzhi = lunar.getDayInGanZhi()
        liuri_gan, liuri_zhi = liuri_ganzhi[0], liuri_ganzhi[1]

        details["第一步_流日干支获取"] = {
            "计算目标": f"获取{target_year}年{target_month}月{target_day}日的干支",
            "使用工具": "lunar_python库",
            "计算过程": f"Solar.fromYmd({target_year}, {target_month}, {target_day}) -> getLunar() -> getDayInGanZhi()",
            "计算结果": f"{target_year}年{target_month}月{target_day}日 = {liuri_ganzhi} (天干:{liuri_gan}, 地支:{liuri_zhi})"
        }

        # 第二步：流日与各方作用分析（完整版）
        sizhu = birth_info['四柱']
        day_gan = sizhu['日柱'][0]
        xiyong_list, ji_list = xiyong_info

        # 流日天干与各方天干的作用
        liuri_tiangan_zuoyong = []

        # 与原局天干作用
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]), ('日干', sizhu['日柱'][0]), ('时干', sizhu['时柱'][0])]:
            relation_result = self._calculate_tiangan_relation(liuri_gan, gan, birth_info, dayun, (liunian_result['流年干支'][0], liunian_result['流年干支'][1]))

            if len(relation_result) == 3:  # 天干五合情况
                relation, huashen_or_coefficient, is_hehua_success = relation_result
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                gan_base_score, gan_xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)

                liuri_shishen = self.shishen_calculator.determine_shishen(day_gan, liuri_gan)
                liuri_base_score, liuri_xiji_type = self._get_xiji_score(liuri_shishen, xiyong_list, ji_list)

                final_score, calculation_detail = self._calculate_wuhe_score(
                    liuri_gan, gan, liuri_base_score, gan_base_score,
                    huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
                )

                liuri_tiangan_zuoyong.append({
                    "对象": f"原局{pos}({gan})",
                    "关系": relation,
                    "十神": shishen,
                    "喜忌": gan_xiji_type,
                    "计算": calculation_detail["总分"],
                    "得分": final_score
                })
            else:  # 普通生克关系
                relation, coefficient = relation_result[0], relation_result[1]
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
                final_score = base_score * coefficient

                liuri_tiangan_zuoyong.append({
                    "对象": f"原局{pos}({gan})",
                    "关系": relation,
                    "十神": shishen,
                    "喜忌": xiji_type,
                    "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                    "得分": final_score
                })

        # 与大运天干作用
        dayun_gan = dayun[0]
        relation_result = self._calculate_tiangan_relation(liuri_gan, dayun_gan, birth_info, dayun, (liunian_result['流年干支'][0], liunian_result['流年干支'][1]))

        if len(relation_result) == 3:  # 天干五合情况
            relation, huashen_or_coefficient, is_hehua_success = relation_result
            shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_gan)
            dayun_base_score, dayun_xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)

            liuri_shishen = self.shishen_calculator.determine_shishen(day_gan, liuri_gan)
            liuri_base_score, liuri_xiji_type = self._get_xiji_score(liuri_shishen, xiyong_list, ji_list)

            final_score, calculation_detail = self._calculate_wuhe_score(
                liuri_gan, dayun_gan, liuri_base_score, dayun_base_score,
                huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
            )

            liuri_tiangan_zuoyong.append({
                "对象": f"大运干({dayun_gan})",
                "关系": relation,
                "十神": shishen,
                "喜忌": dayun_xiji_type,
                "计算": calculation_detail["总分"],
                "得分": final_score
            })
        else:  # 普通生克关系
            relation, coefficient = relation_result[0], relation_result[1]
            shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_gan)
            base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuri_tiangan_zuoyong.append({
                "对象": f"大运干({dayun_gan})",
                "关系": relation,
                "十神": shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 与流年天干作用
        liunian_gan = liunian_result['流年干支'][0]
        relation_result = self._calculate_tiangan_relation(liuri_gan, liunian_gan, birth_info, dayun, (liunian_gan, liunian_result['流年干支'][1]))

        if len(relation_result) == 3:  # 天干五合情况
            relation, huashen_or_coefficient, is_hehua_success = relation_result
            liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
            liunian_base_score, liunian_xiji_type = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)

            liuri_shishen = self.shishen_calculator.determine_shishen(day_gan, liuri_gan)
            liuri_base_score, liuri_xiji_type = self._get_xiji_score(liuri_shishen, xiyong_list, ji_list)

            final_score, calculation_detail = self._calculate_wuhe_score(
                liuri_gan, liunian_gan, liuri_base_score, liunian_base_score,
                huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
            )

            liuri_tiangan_zuoyong.append({
                "对象": f"流年干({liunian_gan})",
                "关系": relation,
                "十神": liunian_shishen,
                "喜忌": liunian_xiji_type,
                "计算": calculation_detail["总分"],
                "得分": final_score
            })
        else:  # 普通生克关系
            relation, coefficient = relation_result[0], relation_result[1]
            liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
            base_score, xiji_type = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuri_tiangan_zuoyong.append({
                "对象": f"流年干({liunian_gan})",
                "关系": relation,
                "十神": liunian_shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 与流月天干作用
        liuyue_gan = liuyue_result['流月干支'][0]
        relation_result = self._calculate_tiangan_relation(liuri_gan, liuyue_gan, birth_info, dayun, (liunian_result['流年干支'][0], liunian_result['流年干支'][1]))

        if len(relation_result) == 3:  # 天干五合情况
            relation, huashen_or_coefficient, is_hehua_success = relation_result
            liuyue_shishen = self.shishen_calculator.determine_shishen(day_gan, liuyue_gan)
            liuyue_base_score, liuyue_xiji_type = self._get_xiji_score(liuyue_shishen, xiyong_list, ji_list)

            liuri_shishen = self.shishen_calculator.determine_shishen(day_gan, liuri_gan)
            liuri_base_score, liuri_xiji_type = self._get_xiji_score(liuri_shishen, xiyong_list, ji_list)

            final_score, calculation_detail = self._calculate_wuhe_score(
                liuri_gan, liuyue_gan, liuri_base_score, liuyue_base_score,
                huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
            )

            liuri_tiangan_zuoyong.append({
                "对象": f"流月干({liuyue_gan})",
                "关系": relation,
                "十神": liuyue_shishen,
                "喜忌": liuyue_xiji_type,
                "计算": calculation_detail["总分"],
                "得分": final_score
            })
        else:  # 普通生克关系
            relation, coefficient = relation_result[0], relation_result[1]
            liuyue_shishen = self.shishen_calculator.determine_shishen(day_gan, liuyue_gan)
            base_score, xiji_type = self._get_xiji_score(liuyue_shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuri_tiangan_zuoyong.append({
                "对象": f"流月干({liuyue_gan})",
                "关系": relation,
                "十神": liuyue_shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 流日地支与各方地支的作用
        liuri_dizhi_zuoyong = []

        # 与原局地支作用
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]), ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            relation, coefficient = self._calculate_dizhi_relation(liuri_zhi, zhi)
            # 地支通过本气确定十神
            zhi_bengqi = list(self.wuxing_calculator.dizhi_info[zhi]['藏干'].keys())[0]
            shishen = self.shishen_calculator.determine_shishen(day_gan, zhi_bengqi)
            base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuri_dizhi_zuoyong.append({
                "对象": f"原局{pos}({zhi})",
                "关系": relation,
                "十神": shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 与大运地支作用
        dayun_zhi = dayun[1]
        relation, coefficient = self._calculate_dizhi_relation(liuri_zhi, dayun_zhi)
        dayun_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[dayun_zhi]['藏干'].keys())[0]
        shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_zhi_bengqi)
        base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
        final_score = base_score * coefficient

        liuri_dizhi_zuoyong.append({
            "对象": f"大运支({dayun_zhi})",
            "关系": relation,
            "十神": shishen,
            "喜忌": xiji_type,
            "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
            "得分": final_score
        })

        # 与流年地支作用
        liunian_zhi = liunian_result['流年干支'][1]
        relation, coefficient = self._calculate_dizhi_relation(liuri_zhi, liunian_zhi)
        liunian_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[liunian_zhi]['藏干'].keys())[0]
        shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_zhi_bengqi)
        base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
        final_score = base_score * coefficient

        liuri_dizhi_zuoyong.append({
            "对象": f"流年支({liunian_zhi})",
            "关系": relation,
            "十神": shishen,
            "喜忌": xiji_type,
            "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
            "得分": final_score
        })

        # 与流月地支作用
        liuyue_zhi = liuyue_result['流月干支'][1]
        relation, coefficient = self._calculate_dizhi_relation(liuri_zhi, liuyue_zhi)
        liuyue_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[liuyue_zhi]['藏干'].keys())[0]
        shishen = self.shishen_calculator.determine_shishen(day_gan, liuyue_zhi_bengqi)
        base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
        final_score = base_score * coefficient

        liuri_dizhi_zuoyong.append({
            "对象": f"流月支({liuyue_zhi})",
            "关系": relation,
            "十神": shishen,
            "喜忌": xiji_type,
            "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
            "得分": final_score
        })

        details["第二步_流日与各方作用分析"] = {
            "流日干支": f"{liuri_gan}{liuri_zhi}",
            "天干作用详情": liuri_tiangan_zuoyong,
            "地支作用详情": liuri_dizhi_zuoyong
        }

        # 第三步：流日综合得分计算
        tiangan_total = sum(item["得分"] for item in liuri_tiangan_zuoyong)
        dizhi_total = sum(item["得分"] for item in liuri_dizhi_zuoyong)
        liuri_subtotal = tiangan_total + dizhi_total

        # 流日总得分需要叠加流年、流月和大运的背景影响
        liunian_score = float(liunian_result['总得分'])
        liuyue_score = float(liuyue_result['总得分'])
        dayun_score = -2.0  # 示例大运得分，实际应该从大运分析中获取
        liuri_total = liuri_subtotal + liunian_score + liuyue_score + dayun_score

        details["第三步_流日综合得分计算"] = {
            "天干小计": f"{tiangan_total:.2f}",
            "地支小计": f"{dizhi_total:.2f}",
            "流日自身得分": f"{liuri_subtotal:.2f}",
            "流年背景分": f"{liunian_score:.2f}",
            "流月背景分": f"{liuyue_score:.2f}",
            "大运背景分": f"{dayun_score:.2f}",
            "计算公式": "流日自身得分 + 流年背景分 + 流月背景分 + 大运背景分",
            "总得分": f"{liuri_total:.2f}"
        }

        # 更新流日结果
        updated_liuri_result = {
            "流日": f"{target_year}年{target_month}月{target_day}日",
            "流日干支": liuri_ganzhi,
            "总得分": liuri_total
        }

        return details, updated_liuri_result

    def _calculate_tiangan_relation(self, gan1, gan2, birth_info=None, dayun=None, liunian_ganzhi=None):
        """计算天干关系，包含详细的天干五合处理"""
        tiangan_info = self.shishen_calculator.tiangan_info

        # 检查天干五合关系
        wuhe_pairs = {
            ('甲', '己'): '土',
            ('乙', '庚'): '金',
            ('丙', '辛'): '水',
            ('丁', '壬'): '木',
            ('戊', '癸'): '火'
        }

        # 查找五合关系
        wuhe_result = None
        for pair, huashen in wuhe_pairs.items():
            if (gan1, gan2) in [pair, pair[::-1]]:
                wuhe_result = huashen
                break

        if wuhe_result:
            # 判断化神是否旺
            is_huashen_wang = self._check_huashen_wang(wuhe_result, birth_info, dayun, liunian_ganzhi)

            if is_huashen_wang:
                return f"天干五合化{wuhe_result}(成功)", wuhe_result, True
            else:
                return f"天干五合合绊", None, False

        # 普通五行生克关系 - 影响系数都是1.0
        wuxing1 = tiangan_info[gan1]['五行']
        wuxing2 = tiangan_info[gan2]['五行']

        if wuxing1 == wuxing2:
            return "比和", 1.0
        elif self.wuxing_calculator.wuxing_sheng.get(wuxing1) == wuxing2:
            return "相生", 1.0
        elif self.wuxing_calculator.wuxing_ke.get(wuxing1) == wuxing2:
            return "相克", 1.0
        else:
            return "无直接关系", 1.0

    def _check_huashen_wang(self, huashen_wuxing, birth_info, dayun, liunian_ganzhi):
        """判断化神是否旺（有强根）"""
        # 化神对应的地支
        wuxing_to_dizhi = {
            '木': ['寅', '卯'],
            '火': ['巳', '午'],
            '土': ['辰', '戌', '丑', '未'],
            '金': ['申', '酉'],
            '水': ['子', '亥']
        }

        huashen_dizhi = wuxing_to_dizhi.get(huashen_wuxing, [])
        huashen_count = 0

        # 统计原局地支中化神的数量
        if birth_info:
            sizhu = birth_info['四柱']
            for pos in ['年柱', '月柱', '日柱', '时柱']:
                zhi = sizhu[pos][1]
                if zhi in huashen_dizhi:
                    huashen_count += 1

        # 统计大运地支
        if dayun and len(dayun) > 1:
            dayun_zhi = dayun[1]
            if dayun_zhi in huashen_dizhi:
                huashen_count += 1

        # 统计流年地支
        if liunian_ganzhi and len(liunian_ganzhi) > 1:
            liunian_zhi = liunian_ganzhi[1]
            if liunian_zhi in huashen_dizhi:
                huashen_count += 1

        # 判断标准：化神地支数量 >= 2 认为化神旺
        return huashen_count >= 2

    def _calculate_dizhi_relation(self, zhi1, zhi2):
        """计算地支关系"""
        # 地支六冲
        liuchong_pairs = [('子', '午'), ('丑', '未'), ('寅', '申'), ('卯', '酉'), ('辰', '戌'), ('巳', '亥')]
        if any((zhi1, zhi2) in [pair, pair[::-1]] for pair in liuchong_pairs):
            return "地支六冲", self.config['地支关系影响系数']['地支六冲']

        # 地支六合
        liuhe_pairs = [('子', '丑'), ('寅', '亥'), ('卯', '戌'), ('辰', '酉'), ('巳', '申'), ('午', '未')]
        if any((zhi1, zhi2) in [pair, pair[::-1]] for pair in liuhe_pairs):
            return "地支六合", self.config['地支关系影响系数']['地支六合']

        # 地支相害
        xianghai_pairs = [('子', '未'), ('丑', '午'), ('寅', '巳'), ('卯', '辰'), ('申', '亥'), ('酉', '戌')]
        if any((zhi1, zhi2) in [pair, pair[::-1]] for pair in xianghai_pairs):
            return "地支相害", self.config['地支关系影响系数']['地支相害']

        # 地支相刑
        if {zhi1, zhi2}.issubset({'寅', '巳', '申'}):
            return "地支相刑", self.config['地支关系影响系数']['地支相刑']
        elif {zhi1, zhi2}.issubset({'丑', '未', '戌'}):
            return "地支相刑", self.config['地支关系影响系数']['地支相刑']
        elif {zhi1, zhi2} == {'子', '卯'}:
            return "地支相刑", self.config['地支关系影响系数']['地支相刑']

        # 普通生克关系
        return "普通生克", 1.0

    def _get_xiji_score(self, shishen, xiyong_list, ji_list):
        """获取喜忌神得分"""
        is_xiyong = any(shishen == item[0] for item in xiyong_list)
        is_ji = any(shishen == item[0] for item in ji_list)

        if is_xiyong:
            return 1.0, "喜用神"
        elif is_ji:
            return -1.0, "忌神"
        else:
            return 0.0, "中性"

    def _calculate_wuhe_score(self, gan1, gan2, gan1_base_score, gan2_base_score, huashen_wuxing, is_hehua_success, xiyong_list, ji_list, day_gan):
        """计算天干五合的详细得分"""
        wuhe_coefficient = self.config['地支关系影响系数']['天干五合']  # 1.5

        if is_hehua_success:
            # 合化成功的计算：(合化后化神分 - 合化前两干分) × 1.5
            # 1. 计算化神分数
            huashen_shishen = self._get_wuxing_shishen(huashen_wuxing, day_gan)
            huashen_base_score, huashen_xiji = self._get_xiji_score(huashen_shishen, xiyong_list, ji_list)

            # 2. 计算合化前后的分数变化
            hehua_hou_score = huashen_base_score  # 合化后的分数
            hehua_qian_score = gan1_base_score + gan2_base_score  # 合化前的分数
            score_change = hehua_hou_score - hehua_qian_score

            # 3. 乘以权重系数
            total_score = score_change * wuhe_coefficient

            calculation_detail = {
                "类型": "合化成功",
                "化神": f"{huashen_wuxing}({huashen_shishen})",
                "合化后": f"{huashen_base_score}",
                "合化前": f"{gan1_base_score} + {gan2_base_score} = {hehua_qian_score:.2f}",
                "分数变化": f"{hehua_hou_score:.2f} - ({hehua_qian_score:.2f}) = {score_change:.2f}",
                "总分": f"{score_change:.2f} × {wuhe_coefficient} = {total_score:.2f}"
            }

        else:
            # 合绊的计算：(甲基准分 + 己基准分) × (-0.33) × 1.5
            normal_total = gan1_base_score + gan2_base_score
            total_score = normal_total * (-0.33) * wuhe_coefficient

            calculation_detail = {
                "类型": "合绊",
                "正常总分": f"{gan1_base_score} + {gan2_base_score} = {normal_total:.2f}",
                "合绊公式": f"({normal_total:.2f}) × (-0.33) × {wuhe_coefficient} = {total_score:.2f}",
                "总分": f"{total_score:.2f}"
            }

        return total_score, calculation_detail

    def _get_wuxing_shishen(self, wuxing, day_gan):
        """根据五行和日干确定十神"""
        day_wuxing = self.shishen_calculator.tiangan_info[day_gan]['五行']

        if wuxing == day_wuxing:
            return "比肩"  # 简化处理，实际应该区分比肩和劫财
        elif self.wuxing_calculator.wuxing_sheng.get(day_wuxing) == wuxing:
            return "食神"  # 简化处理，实际应该区分食神和伤官
        elif self.wuxing_calculator.wuxing_sheng.get(wuxing) == day_wuxing:
            return "正印"  # 简化处理，实际应该区分正印和偏印
        elif self.wuxing_calculator.wuxing_ke.get(day_wuxing) == wuxing:
            return "正财"  # 简化处理，实际应该区分正财和偏财
        elif self.wuxing_calculator.wuxing_ke.get(wuxing) == day_wuxing:
            return "正官"  # 简化处理，实际应该区分正官和七杀
        else:
            return "未知"

    def analyze_marriage_compatibility(self):
        """合婚分析（预留接口）"""
        return {
            "状态": "功能暂未实现",
            "说明": "合婚计算模块预留接口，后续开发"
        }
    
    def format_comprehensive_output(self, result, show_details=True):
        """格式化综合输出结果"""
        if "错误" in result:
            return f"分析失败：{result['错误']}"
        
        basic_info = result["基本信息"]
        wuxing_info = result["五行分析"]
        shishen_info = result["十神分析"]
        dayun_info = result["大运流年"]
        target_date = self.config['流年流月流日分析日期']
        
        output = f"""
{'='*50}
八字占卜系统 - 综合分析报告
{'='*50}

【基本信息】
姓名：{basic_info['姓名']}
性别：男  # 可根据需要调整
出生地：{basic_info['出生地']} (经度: {basic_info['经度']}°)

真太阳时计算详细过程：
1. 北京时间：{basic_info['北京时间']}
2. 经度时差：{basic_info['经度时差']} (公式: (120° - 当地经度) × 4分钟)
3. 均时差：{basic_info['均时差']} (地球椭圆轨道修正)
4. 真太阳时：{basic_info['真太阳时']} (北京时间 + 经度时差 + 均时差)
5. 阴历日期：{basic_info['阴历信息']['阴历年']}年{basic_info['阴历信息']['阴历月']}月{basic_info['阴历信息']['阴历日']}日 {basic_info['阴历信息']['阴历时']}时

【四柱八字】
年柱：{basic_info['四柱']['年柱']}
月柱：{basic_info['四柱']['月柱']}
日柱：{basic_info['四柱']['日柱']}
时柱：{basic_info['四柱']['时柱']}
节气：{basic_info['阴历信息']['节气']}

【五行分析】
当前季节：{wuxing_info['当前季节']}

五行计算详细过程：
1. 天干五行统计：
"""
        for wuxing, positions in wuxing_info['计算详情']['天干五行统计'].items():
            output += f"   {wuxing}：{', '.join(positions)}\n"

        output += f"""
2. 地支本气统计：
"""
        for wuxing, positions in wuxing_info['计算详情']['地支本气统计'].items():
            output += f"   {wuxing}：{', '.join(positions)}\n"

        output += f"""
3. 地支藏干详情：
"""
        for position, canggan_list in wuxing_info['计算详情']['地支藏干详情'].items():
            output += f"   {position}：{', '.join(canggan_list)}\n"

        output += f"""
4. 季节旺度影响：
   当前季节：{wuxing_info['计算详情']['季节旺度影响']['当前季节']}
   得令（旺）：{wuxing_info['计算详情']['季节旺度影响']['旺']}
   失令（死）：{wuxing_info['计算详情']['季节旺度影响']['死']}
   平和：{wuxing_info['计算详情']['季节旺度影响']['其他']}

5. 五行力量详细计算过程：

第一步 - 基础分数统计：
   天干统计：
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第一步_基础分数统计']['天干统计']:
            output += f"     {detail}\n"

        output += f"""   地支本气统计：
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第一步_基础分数统计']['地支本气统计']:
            output += f"     {detail}\n"

        output += f"""   基础分数：{wuxing_info['计算详情']['五行力量详细计算']['第一步_基础分数统计']['基础分数']}

第二步 - 季节旺度调整：
   调整公式：{wuxing_info['计算详情']['五行力量详细计算']['第二步_季节旺度调整']['调整公式']}
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第二步_季节旺度调整']['详细计算']:
            output += f"     {detail}\n"

        output += f"""   调整后分数：{wuxing_info['计算详情']['五行力量详细计算']['第二步_季节旺度调整']['调整后分数']}

第三步 - 藏干权重计算：
   计算说明：{wuxing_info['计算详情']['五行力量详细计算']['第三步_藏干权重计算']['计算说明']}
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第三步_藏干权重计算']['详细计算']:
            output += f"     {detail}\n"

        output += f"""   藏干得分：{wuxing_info['计算详情']['五行力量详细计算']['第三步_藏干权重计算']['藏干得分']}

第四步 - 生扶克制计算：
   基础分数：{wuxing_info['计算详情']['五行力量详细计算']['第四步_生扶克制计算']['基础分数']}
   生扶系数：{wuxing_info['计算详情']['五行力量详细计算']['第四步_生扶克制计算']['生扶系数']}
   克制系数：{wuxing_info['计算详情']['五行力量详细计算']['第四步_生扶克制计算']['克制系数']}
   生扶计算：
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第四步_生扶克制计算']['生扶计算']:
            output += f"     {detail}\n"

        output += f"""   克制计算：
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第四步_生扶克制计算']['克制计算']:
            output += f"     {detail}\n"

        output += f"""
第五步 - 最终力量汇总：
   最终得分：{wuxing_info['计算详情']['五行力量详细计算']['第五步_最终力量汇总']['最终得分']}
   总力量：{wuxing_info['计算详情']['五行力量详细计算']['第五步_最终力量汇总']['总力量']}
   力量占比：{wuxing_info['计算详情']['五行力量详细计算']['第五步_最终力量汇总']['力量占比']}
   状态判定：
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第五步_最终力量汇总']['状态判定']:
            output += f"     {detail}\n"

        output += f"""
6. 五行力量转换过程：
   说明：从第五步的详细计算分数转换为最终的简化力量值
   转换规则：使用不同的计算方法重新计算，考虑季节旺度和生扶克制
"""

        # 添加转换过程的详细计算说明
        wuxing_detailed_scores = wuxing_info['计算详情']['五行力量详细计算']['第五步_最终力量汇总']['最终得分']

        # 获取转换计算的详细过程
        conversion_details = self._calculate_wuxing_conversion_details(basic_info['四柱'], wuxing_detailed_scores, wuxing_info['五行力量'])

        for wuxing, info in wuxing_info['五行力量'].items():
            detailed_score = wuxing_detailed_scores.get(wuxing, "未知")
            conversion_detail = conversion_details.get(wuxing, {})

            output += f"   {wuxing}：{detailed_score} → {info['力量值']} ({info['状态']}, {info['季节状态']})\n"

            if conversion_detail:
                output += f"     转换计算过程：\n"
                for step, value in conversion_detail.items():
                    output += f"       {step}: {value}\n"

        output += f"""
7. 最终五行力量分布：
"""

        for wuxing, info in wuxing_info['五行力量'].items():
            output += f"   {wuxing}：{info['力量值']} ({info['状态']}, {info['季节状态']})\n"
        
        body_strength, support_power, drain_power = shishen_info['身强身弱']
        xiyong_list, ji_list = shishen_info['喜用神忌神']
        
        output += f"""
【十神分析】
日主信息：{shishen_info['计算详情']['日主信息']['日干']} ({shishen_info['计算详情']['日主信息']['五行']}{shishen_info['计算详情']['日主信息']['阴阳']})

十神计算详细过程：
1. 天干十神分析：
"""
        for position, analysis in shishen_info['计算详情']['天干十神分析'].items():
            output += f"   {position}：{analysis}\n"

        output += f"""
2. 地支藏干十神分析：
"""
        for position, canggan_list in shishen_info['计算详情']['地支藏干十神分析'].items():
            output += f"   {position}：\n"
            for canggan_analysis in canggan_list:
                output += f"     {canggan_analysis}\n"

        output += f"""
3. 十神权重汇总：
"""
        for shishen, weight in shishen_info['十神权重'].items():
            output += f"   {shishen}：{weight:.2f}\n"

        output += f"""
4. 身强身弱判断：
   生扶日主力量：{support_power:.2f} (正印+偏印+比肩+劫财)
   克泄耗日主力量：{drain_power:.2f} (正官+七杀+食神+伤官+正财+偏财)
   判断结果：{body_strength}
"""
        
        output += f"""
喜用神："""
        for shishen, weight in xiyong_list[:4]:
            output += f" {shishen}({weight:.2f})"
        
        output += f"""
忌神："""
        for shishen, weight in ji_list[:4]:
            output += f" {shishen}({weight:.2f})"
        
        output += f"""

夫妻宫：{shishen_info['夫妻宫配偶星']['夫妻宫']}
配偶星：{shishen_info['夫妻宫配偶星']['配偶星']}

【大运流年分析】

大运起运计算详细过程：
1. 起运规则判断：
   年干：{dayun_info['计算详情']['起运规则判断']['年干']}
   性别：{dayun_info['计算详情']['起运规则判断']['性别']}
   适用规则：{dayun_info['计算详情']['起运规则判断']['适用规则']}
   排列方向：{dayun_info['计算详情']['起运规则判断']['排列方向']}

2. 节气距离计算：
   {dayun_info['计算详情']['节气距离计算']['距离节气天数']}
   计算方法：{dayun_info['计算详情']['节气距离计算']['计算方法']}

3. 起运时间换算：
   换算公式：{dayun_info['计算详情']['起运时间换算']['换算公式']}
   计算过程：{dayun_info['计算详情']['起运时间换算']['计算过程']}
   月份计算：{dayun_info['计算详情']['起运时间换算']['月份计算']}
   最终结果：{dayun_info['计算详情']['起运时间换算']['最终结果']}

4. 大运排列规则：
   起始月柱：{dayun_info['计算详情']['大运排列规则']['起始月柱']}
   排列方向：{dayun_info['计算详情']['大运排列规则']['排列方向']}
   每步大运：{dayun_info['计算详情']['大运排列规则']['每步大运']}

5. 大运序列：
"""

        for dayun, (start_age, end_age) in dayun_info['大运序列']:
            output += f"   {start_age}-{end_age}岁：{dayun}运\n"
        
        liunian = dayun_info['流年分析']
        liuyue = dayun_info['流月分析']
        liuri = dayun_info['流日分析']

        output += f"""
6. 流年流月流日计算详细过程：

{dayun_info['流年计算详情']['第一步_流年干支获取']['计算目标']}：
   使用工具：{dayun_info['流年计算详情']['第一步_流年干支获取']['使用工具']}
   计算过程：{dayun_info['流年计算详情']['第一步_流年干支获取']['计算过程']}
   计算结果：{dayun_info['流年计算详情']['第一步_流年干支获取']['计算结果']}

原局四柱分析：
   日主：{dayun_info['流年计算详情']['第二步_原局四柱分析']['日主']}
   原局四柱：
"""
        for pos, info in dayun_info['流年计算详情']['第二步_原局四柱分析']['原局四柱'].items():
            output += f"     {pos}：{info}\n"

        output += f"""
喜忌神权重计算：
   计算依据：{dayun_info['流年计算详情']['第三步_喜忌神权重计算']['计算依据']}
   喜用神：{', '.join(dayun_info['流年计算详情']['第三步_喜忌神权重计算']['喜用神'])}
   忌神：{', '.join(dayun_info['流年计算详情']['第三步_喜忌神权重计算']['忌神'])}
   权重说明：{dayun_info['流年计算详情']['第三步_喜忌神权重计算']['权重说明']}

流年与原局作用关系详细计算：
   流年干支：{dayun_info['流年计算详情']['第四步_流年与原局作用关系']['流年干支']}
   天干作用详情：
"""
        for zuoyong in dayun_info['流年计算详情']['第四步_流年与原局作用关系']['天干作用详情']:
            output += f"     {zuoyong['原局']} - {zuoyong['五行关系']}\n"
            output += f"       十神：{zuoyong['十神']} ({zuoyong['喜忌']})\n"
            output += f"       计算：{zuoyong['计算公式']}\n"

            # 如果有详细过程，展示出来
            if '详细过程' in zuoyong:
                detail = zuoyong['详细过程']
                if detail['类型'] == '合化成功':
                    output += f"         化神：{detail['化神']}\n"
                    output += f"         合化后：{detail['合化后']}\n"
                    output += f"         合化前：{detail['合化前']}\n"
                    output += f"         分数变化：{detail['分数变化']}\n"
                    output += f"         最终得分：{detail['总分']}\n"
                elif detail['类型'] == '合绊':
                    output += f"         正常总分：{detail['正常总分']}\n"
                    output += f"         合绊公式：{detail['合绊公式']}\n"

        output += f"""
综合得分计算：
   计算公式：{dayun_info['流年计算详情']['第五步_综合得分计算']['计算公式']}
   天干得分：{dayun_info['流年计算详情']['第五步_综合得分计算']['天干得分详细']} = {dayun_info['流年计算详情']['第五步_综合得分计算']['天干小计']}
   地支得分：{dayun_info['流年计算详情']['第五步_综合得分计算']['地支得分详细']} = {dayun_info['流年计算详情']['第五步_综合得分计算']['地支小计']}
   总得分：{dayun_info['流年计算详情']['第五步_综合得分计算']['总得分']}

吉凶等级判定：
   判定标准：
"""
        for level, standard in dayun_info['流年计算详情']['第六步_吉凶等级判定']['判定标准'].items():
            output += f"     {level}：{standard}\n"

        output += f"""   实际得分：{dayun_info['流年计算详情']['第六步_吉凶等级判定']['实际得分']}
   判定结果：{dayun_info['流年计算详情']['第六步_吉凶等级判定']['判定结果']}
   运势解读：{dayun_info['流年计算详情']['第六步_吉凶等级判定']['运势解读']}

7. 流月计算详细过程：

{dayun_info['流月计算详情']['第一步_流月干支获取']['计算目标']}：
   使用工具：{dayun_info['流月计算详情']['第一步_流月干支获取']['使用工具']}
   计算过程：{dayun_info['流月计算详情']['第一步_流月干支获取']['计算过程']}
   计算结果：{dayun_info['流月计算详情']['第一步_流月干支获取']['计算结果']}

流月与各方作用分析：
   流月干支：{dayun_info['流月计算详情']['第二步_流月与各方作用分析']['流月干支']}
   天干作用详情：
"""
        for zuoyong in dayun_info['流月计算详情']['第二步_流月与各方作用分析']['天干作用详情']:
            output += f"     {zuoyong['对象']} - {zuoyong['关系']}\n"
            output += f"       十神：{zuoyong['十神']} ({zuoyong['喜忌']})\n"
            output += f"       计算：{zuoyong['计算']}\n"

        output += f"""   地支作用详情：
"""
        for zuoyong in dayun_info['流月计算详情']['第二步_流月与各方作用分析']['地支作用详情']:
            output += f"     {zuoyong['对象']} - {zuoyong['关系']}\n"
            output += f"       十神：{zuoyong['十神']} ({zuoyong['喜忌']})\n"
            output += f"       计算：{zuoyong['计算']}\n"

        output += f"""
流月综合得分计算：
   天干小计：{dayun_info['流月计算详情']['第三步_流月综合得分计算']['天干小计']}
   地支小计：{dayun_info['流月计算详情']['第三步_流月综合得分计算']['地支小计']}
   流月自身得分：{dayun_info['流月计算详情']['第三步_流月综合得分计算']['流月自身得分']}
   流年背景分：{dayun_info['流月计算详情']['第三步_流月综合得分计算']['流年背景分']}
   大运背景分：{dayun_info['流月计算详情']['第三步_流月综合得分计算']['大运背景分']}
   计算公式：{dayun_info['流月计算详情']['第三步_流月综合得分计算']['计算公式']}
   总得分：{dayun_info['流月计算详情']['第三步_流月综合得分计算']['总得分']}

8. 流日计算详细过程：

{dayun_info['流日计算详情']['第一步_流日干支获取']['计算目标']}：
   使用工具：{dayun_info['流日计算详情']['第一步_流日干支获取']['使用工具']}
   计算过程：{dayun_info['流日计算详情']['第一步_流日干支获取']['计算过程']}
   计算结果：{dayun_info['流日计算详情']['第一步_流日干支获取']['计算结果']}

流日与各方作用分析：
   流日干支：{dayun_info['流日计算详情']['第二步_流日与各方作用分析']['流日干支']}
   天干作用详情：
"""
        for zuoyong in dayun_info['流日计算详情']['第二步_流日与各方作用分析']['天干作用详情']:
            output += f"     {zuoyong['对象']} - {zuoyong['关系']}\n"
            output += f"       十神：{zuoyong['十神']} ({zuoyong['喜忌']})\n"
            output += f"       计算：{zuoyong['计算']}\n"

        output += f"""   地支作用详情：
"""
        for zuoyong in dayun_info['流日计算详情']['第二步_流日与各方作用分析']['地支作用详情']:
            output += f"     {zuoyong['对象']} - {zuoyong['关系']}\n"
            output += f"       十神：{zuoyong['十神']} ({zuoyong['喜忌']})\n"
            output += f"       计算：{zuoyong['计算']}\n"

        output += f"""
流日综合得分计算：
   天干小计：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['天干小计']}
   地支小计：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['地支小计']}
   流日自身得分：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['流日自身得分']}
   流年背景分：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['流年背景分']}
   流月背景分：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['流月背景分']}
   大运背景分：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['大运背景分']}
   计算公式：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['计算公式']}
   总得分：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['总得分']}

9. 流年流月流日分析结果：

流年分析：{dayun_info['流年分析']['流年']} ({dayun_info['流年分析']['流年干支']})
  总得分：{dayun_info['流年分析']['总得分']:.2f}
  吉凶等级：{dayun_info['流年分析']['吉凶等级']}
  运势解读：{dayun_info['流年分析']['运势解读']}

流月分析：{dayun_info['流月分析']['流月']} ({dayun_info['流月分析']['流月干支']})
  总得分：{dayun_info['流月分析']['总得分']:.2f}

流日分析：{dayun_info['流日分析']['流日']} ({dayun_info['流日分析']['流日干支']})
  总得分：{dayun_info['流日分析']['总得分']:.2f}

【合婚分析】
{result['合婚分析']['状态']}
{result['合婚分析']['说明']}

{'='*50}
分析完成
{'='*50}
"""
        
        return output

    def format_simple_output(self, result):
        """格式化简洁版输出结果（不包含详细计算过程）"""
        if "错误" in result:
            return f"分析失败：{result['错误']}"

        basic_info = result["基本信息"]
        wuxing_info = result["五行分析"]
        shishen_info = result["十神分析"]
        dayun_info = result["大运流年"]

        body_strength, support_power, drain_power = shishen_info['身强身弱']
        xiyong_list, ji_list = shishen_info['喜用神忌神']

        output = f"""
{'='*50}
八字占卜系统 - 分析报告
{'='*50}

【基本信息】
姓名：{basic_info['姓名']}
出生地：{basic_info['出生地']} (经度: {basic_info['经度']}°)
北京时间：{basic_info['北京时间']}
真太阳时：{basic_info['真太阳时']}

【四柱八字】
年柱：{basic_info['四柱']['年柱']}
月柱：{basic_info['四柱']['月柱']}
日柱：{basic_info['四柱']['日柱']}
时柱：{basic_info['四柱']['时柱']}

【五行分析】
当前季节：{wuxing_info['当前季节']}
五行力量分布：
"""

        for wuxing, info in wuxing_info['五行力量'].items():
            output += f"  {wuxing}：{info['力量值']} ({info['状态']}, {info['季节状态']})\n"

        output += f"""
【十神分析】
日主：{basic_info['四柱']['日柱'][0]}
身强身弱：{body_strength} (生扶力量: {support_power:.2f}, 克泄耗力量: {drain_power:.2f})

十神权重分布：
"""
        for shishen, weight in shishen_info['十神权重'].items():
            output += f"  {shishen}：{weight:.2f}\n"

        output += f"""
喜用神："""
        for shishen, weight in xiyong_list[:4]:
            output += f" {shishen}({weight:.2f})"

        output += f"""
忌神："""
        for shishen, weight in ji_list[:4]:
            output += f" {shishen}({weight:.2f})"

        output += f"""

夫妻宫：{shishen_info['夫妻宫配偶星']['夫妻宫']}
配偶星：{shishen_info['夫妻宫配偶星']['配偶星']}

【大运流年分析】
起运：{dayun_info['起运信息']['起运方向']} {dayun_info['起运信息']['起运岁数']}岁{dayun_info['起运信息']['起运月份']}个月

大运序列：
"""

        for dayun, (start_age, end_age) in dayun_info['大运序列']:
            output += f"  {start_age}-{end_age}岁：{dayun}运\n"

        liunian = dayun_info['流年分析']
        liuyue = dayun_info['流月分析']
        liuri = dayun_info['流日分析']

        output += f"""
流年分析：{liunian['流年']} ({liunian['流年干支']})
  吉凶等级：{liunian['吉凶等级']} (得分: {liunian['总得分']})
  运势解读：{liunian['运势解读']}

流月分析：{liuyue['流月']} ({liuyue['流月干支']})
  得分：{liuyue['总得分']}

流日分析：{liuri['流日']} ({liuri['流日干支']})
  得分：{liuri['总得分']}

【合婚分析】
{result['合婚分析']['状态']}
{result['合婚分析']['说明']}

{'='*50}
分析完成
{'='*50}
"""

        return output

    def get_api_result(self, result):
        """获取API格式的结果（为前端接口预留）"""
        if "错误" in result:
            return {"success": False, "error": result["错误"]}

        return {
            "success": True,
            "data": {
                "basic_info": result["基本信息"],
                "wuxing_analysis": result["五行分析"],
                "shishen_analysis": result["十神分析"],
                "dayun_analysis": result["大运流年"],
                "marriage_analysis": result["合婚分析"]
            }
        }


# 测试函数
def test_comprehensive_analyzer():
    """测试综合分析器"""
    analyzer = ComprehensiveAnalyzer()
    
    # 进行完整分析
    result = analyzer.analyze_complete_bazi(
        birth_year=1990,
        birth_month=5,
        birth_day=15,
        birth_hour=14,
        birth_minute=30,
        city_name="广州",
        name="测试用户",
        gender="男"
    )
    
    # 输出格式化结果
    formatted_output = analyzer.format_comprehensive_output(result)
    print(formatted_output)
    
    return result


if __name__ == "__main__":
    test_comprehensive_analyzer()
