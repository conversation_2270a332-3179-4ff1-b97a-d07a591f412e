#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的农历转换库测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime

def test_libraries():
    """测试各个农历转换库"""
    print("🌙 农历转换库测试")
    print("=" * 50)
    
    # 测试日期
    test_date = (1990, 9, 2)
    year, month, day = test_date
    print(f"测试日期: {year}年{month}月{day}日")
    print()
    
    # 测试zhdate
    try:
        from zhdate import ZhDate
        solar_date = datetime(year, month, day)
        lunar_date = ZhDate.from_datetime(solar_date)
        print(f"✅ zhdate: {lunar_date} (闰月: {getattr(lunar_date, 'isleap', '未知')})")
    except Exception as e:
        print(f"❌ zhdate: {e}")
    
    # 测试chinese-lunar-calendar-converter
    try:
        from chinese_lunar_calendar_converter import solar_to_lunar
        solar_date_str = f"{year}-{month:02d}-{day:02d}"
        lunar_info = solar_to_lunar(solar_date_str)
        print(f"✅ chinese-lunar-calendar-converter: {lunar_info[0]}年{lunar_info[1]}月{lunar_info[2]}日")
    except Exception as e:
        print(f"❌ chinese-lunar-calendar-converter: {e}")
    
    # 测试lunardate
    try:
        import lunardate
        solar_date = datetime(year, month, day)
        lunar_date = lunardate.LunarDate.from_datetime(solar_date)
        print(f"✅ lunardate: {lunar_date} (闰月: {getattr(lunar_date, 'isleap', '未知')})")
    except Exception as e:
        # 尝试其他API
        try:
            lunar_date = lunardate.LunarDate(year, month, day)
            print(f"✅ lunardate: {lunar_date} (闰月: {getattr(lunar_date, 'isleap', '未知')})")
        except Exception as e2:
            print(f"❌ lunardate: {e2}")
    
    # 测试我们的转换器
    try:
        from utils.calendar_converter import CalendarConverter
        converter = CalendarConverter()
        result = converter.solar_to_lunar(year, month, day)
        if result:
            print(f"✅ 自研转换器: {result['lunar_date_full']} (精度: {result['conversion_accuracy']})")
        else:
            print("❌ 自研转换器: 返回None")
    except Exception as e:
        print(f"❌ 自研转换器: {e}")

if __name__ == "__main__":
    test_libraries()
