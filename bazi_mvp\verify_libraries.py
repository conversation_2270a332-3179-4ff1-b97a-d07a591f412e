#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证农历转换库安装
"""

def test_zhdate():
    """测试zhdate库"""
    try:
        from zhdate import ZhDate
        from datetime import datetime
        
        # 测试转换
        solar_date = datetime(1990, 9, 2)
        lunar_date = ZhDate.from_datetime(solar_date)
        
        print("✅ zhdate库安装成功")
        print(f"   测试结果: {lunar_date}")
        print(f"   农历年份: {lunar_date.year}")
        print(f"   农历月份: {lunar_date.month}")
        print(f"   农历日期: {lunar_date.day}")
        return True
    except Exception as e:
        print(f"❌ zhdate库测试失败: {e}")
        return False

def test_lunardate():
    """测试lunardate库"""
    try:
        import lunardate
        from datetime import datetime
        
        # 测试转换
        solar_date = datetime(1990, 9, 2)
        lunar_date = lunardate.LunarDate.from_datetime(solar_date)
        
        print("✅ lunardate库安装成功")
        print(f"   测试结果: {lunar_date}")
        print(f"   农历年份: {lunar_date.year}")
        print(f"   农历月份: {lunar_date.month}")
        print(f"   农历日期: {lunar_date.day}")
        return True
    except Exception as e:
        print(f"❌ lunardate库测试失败: {e}")
        return False

def test_chinese_lunar_calendar_converter():
    """测试chinese-lunar-calendar-converter库"""
    try:
        from chinese_lunar_calendar_converter import solar_to_lunar
        
        # 测试转换
        solar_date_str = "1990-09-02"
        lunar_info = solar_to_lunar(solar_date_str)
        
        print("✅ chinese-lunar-calendar-converter库安装成功")
        print(f"   测试结果: {lunar_info}")
        print(f"   农历年份: {lunar_info[0]}")
        print(f"   农历月份: {lunar_info[1]}")
        print(f"   农历日期: {lunar_info[2]}")
        return True
    except Exception as e:
        print(f"❌ chinese-lunar-calendar-converter库测试失败: {e}")
        return False

def test_our_converter():
    """测试我们的转换器"""
    try:
        from utils.calendar_converter import CalendarConverter
        
        converter = CalendarConverter()
        result = converter.solar_to_lunar(1990, 9, 2)
        
        if result:
            print("✅ 自研转换器工作正常")
            print(f"   测试结果: {result['lunar_date_full']}")
            print(f"   农历年份: {result['lunar_year']}")
            print(f"   农历月份: {result['lunar_month']}")
            print(f"   农历日期: {result['lunar_day']}")
            return True
        else:
            print("❌ 自研转换器返回None")
            return False
    except Exception as e:
        print(f"❌ 自研转换器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 验证农历转换库安装情况")
    print("=" * 50)
    
    results = []
    
    print("\n1. 测试zhdate库:")
    results.append(test_zhdate())
    
    print("\n2. 测试lunardate库:")
    results.append(test_lunardate())
    
    print("\n3. 测试chinese-lunar-calendar-converter库:")
    results.append(test_chinese_lunar_calendar_converter())
    
    print("\n4. 测试自研转换器:")
    results.append(test_our_converter())
    
    print(f"\n📊 测试总结:")
    print(f"成功: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 所有库都安装成功！")
    else:
        print("⚠️  部分库安装失败，请检查")

if __name__ == "__main__":
    main()

